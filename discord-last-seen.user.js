// ==UserScript==
// @name        Discord聊天用户最后发言时间
// @namespace   Violentmonkey Scripts
// @match       https://discord.com/channels/*
// @grant       none
// @version     1.3
// <AUTHOR>
// @description 在Discord成员列表中显示用户最后发言时间，并用 ✅/❌ 标示最近一小时内是否活跃。
// ==/UserScript==

(function() {
    'use strict';

    console.log('[Discord最后发言时间] 脚本开始运行 (v1.3)...');

    // Discord的CSS类名是动态生成的，所以这里使用更通用的属性选择器来定位元素
    const SELECTORS = {
        messageList: '[data-list-id="chat-messages"]',
        messageItem: 'li[id^="chat-messages-"]',
        usernameInMessage: '[id^="message-username-"]', 
        timestampInMessage: '[id^="message-timestamp-"]',
        // --- 选择器已根据新提供的HTML结构更新 ---
        memberList: 'div[data-list-id^="members-"]', // 更精确地定位到成员滚动的容器
        memberItem: 'div[class*="member__"][role="listitem"]', // 定位到容器内具体的每个成员项
        usernameInMember: 'span[class*="name__"][class*="username__"]', // 定位到包含用户名的最内层span
        subTextInMember: 'div[class*="subText__"]' // 用来显示时间的元素
    };


    function updateLastMessageTimes() {
        console.log('[Discord最后发言时间] 开始更新最后发言时间...');
        const userLastMessage = new Map();

        // 1. 从聊天记录中收集每个用户的最后发言时间和发言内容
        const messageList = document.querySelector(SELECTORS.messageList);
        if (!messageList) {
            console.log('[Discord最后发言时间] 错误: 未找到聊天消息列表。选择器:', SELECTORS.messageList);
            return;
        }
        console.log('[Discord最后发言时间] 成功找到聊天消息列表。');

        const messages = messageList.querySelectorAll(SELECTORS.messageItem);
        console.log(`[Discord最后发言时间] 找到了 ${messages.length} 条消息。`);

        messages.forEach(message => {
            const usernameElem = message.querySelector(SELECTORS.usernameInMessage);
            const timeElem = message.querySelector(SELECTORS.timestampInMessage);

            if (usernameElem && timeElem) {
                const username = usernameElem.textContent.trim();
                const displayTimestamp = timeElem.getAttribute('aria-label') || timeElem.textContent.trim();
                const isoTimestamp = timeElem.getAttribute('datetime'); // 获取精确的ISO格式时间

                if (username && isoTimestamp) {
                    userLastMessage.set(username.toLowerCase(), {
                        display: displayTimestamp,
                        iso: isoTimestamp
                    });
                }
            }
        });

        if (userLastMessage.size === 0) {
            console.log('[Discord最后发言时间] 警告: 未能从消息中提取到任何用户信息和时间。');
            // Do not return, because we still want to mark members as "not seen"
        }
        console.log('[Discord最后发言时间] 成功收集到以下用户的最后发言时间:', userLastMessage);


        // 2. 在成员列表中更新或添加时间戳
        const memberList = document.querySelector(SELECTORS.memberList);
        if(!memberList) {
            console.log('[Discord最后发言时间] 错误: 未找到成员列表。选择器:', SELECTORS.memberList);
            return;
        }
        console.log('[Discord最后发言时间] 成功找到成员列表。', memberList);
        
        const members = memberList.querySelectorAll(SELECTORS.memberItem);
        console.log(`[Discord最后发言时间] 找到了 ${members.length} 位成员。`);

        members.forEach(member => {
            const usernameElem = member.querySelector(SELECTORS.usernameInMember);
            const subTextElem = member.querySelector(SELECTORS.subTextInMember);

            if (usernameElem && subTextElem) {
                const memberUsername = usernameElem.textContent.trim().toLowerCase();
                const lastMessageData = userLastMessage.get(memberUsername);
                
                let timestampElem = subTextElem.querySelector('.last-message-timestamp');
                if (!timestampElem) {
                    timestampElem = document.createElement('div');
                    timestampElem.className = 'last-message-timestamp';
                    timestampElem.style.color = 'var(--text-muted)';
                    timestampElem.style.fontSize = '12px';
                    timestampElem.style.fontWeight = '400';
                    subTextElem.appendChild(timestampElem);
                }

                if (lastMessageData) {
                    const lastMessageDate = new Date(lastMessageData.iso);
                    const now = new Date();
                    const oneHourInMs = 60 * 60 * 1000;
                    
                    const isWithinLastHour = (now.getTime() - lastMessageDate.getTime()) < oneHourInMs;
                    const icon = isWithinLastHour ? '✅' : '❌';
                    
                    timestampElem.textContent = `${icon} ${lastMessageData.display}`;
                    console.log(`[Discord最后发言时间] 更新成员 "${memberUsername}" 为: ${icon}`);
                } else {
                    // 对在成员列表但未在聊天记录中找到的成员进行处理
                    timestampElem.textContent = `❌ 未见发言`;
                }

            } else {
                 if (!usernameElem) console.log('[Discord最后发言时间] 警告: 在一个成员项中未能找到用户名元素。选择器:', SELECTORS.usernameInMember, member);
                 if (!subTextElem) console.log('[Discord最后发言时间] 警告: 在一个成员项中未能找到子文本元素。选择器:', SELECTORS.subTextInMember, member);
            }
        });
        console.log('[Discord最后发言时间] 更新完成。');
    }

    // 定期执行, 以便更新新消息和成员列表的变化
    const observer = new MutationObserver((mutations) => {
        // 做一个简单的防抖，避免短时间内过于频繁的更新
        let timeout;
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            console.log("[Discord最后发言时间] 检测到页面变化，准备更新...");
            updateLastMessageTimes();
        }, 500);
    });
    
    // 等待聊天和成员列表加载
    const startObserving = () => {
        console.log('[Discord最后发言时间] 正在尝试启动观察者...');
        const messageList = document.querySelector(SELECTORS.messageList);
        const memberList = document.querySelector(SELECTORS.memberList);

        if(messageList && memberList) {
             console.log('[Discord最后发言时间] 聊天列表和成员列表均已加载，启动观察者并执行首次更新。');
             observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            updateLastMessageTimes(); // 立即执行一次
        } else {
            console.log(`[Discord最后发言时间] 等待元素加载... 聊天列表: ${!!messageList}, 成员列表: ${!!memberList}. 1秒后重试。`);
            setTimeout(startObserving, 1000); // 如果没找到就1秒后重试
        }
    };
    
    startObserving();

})();