// ==UserScript==
// @name         Telegram 最后发言时间
// @namespace    http://tampermonkey.net/
// @version      1.2
// @description  在 Telegram 网页版成员列表中显示用户的最后发言时间及状态
// <AUTHOR>
// @match        https://web.telegram.org/k/*
// @grant        none
// @run-at       document-idle
// ==/UserScript==

(function() {
    'use strict';

    console.log('[Telegram 最后发言时间] 脚本已启动 (v1.2)');

    const lastSeen = {};

    function processMessages() {
        console.log('[Telegram 最后发言时间] 开始处理聊天记录...');
        // 基于新提供的HTML更新了消息选择器
        const messages = document.querySelectorAll('.bubble[data-timestamp]');
        console.log(`[Telegram 最后发言时间] 找到 ${messages.length} 条消息`);

        messages.forEach(message => {
            const usernameEl = message.querySelector('.peer-title');
            const timestamp = message.dataset.timestamp;

            if (usernameEl && timestamp) {
                const username = usernameEl.innerText.trim();
                // Unix timestamp (seconds) to milliseconds for Date object
                const time = new Date(parseInt(timestamp, 10) * 1000);

                if (!lastSeen[username] || time > lastSeen[username]) {
                    lastSeen[username] = time;
                }
            }
        });
        console.log('[Telegram 最后发言时间] 聊天记录处理完毕。');
        // For debugging, let's see what we collected
        console.log('[Telegram 最后发言时间] 收集到的用户发言时间:', lastSeen);
    }

    function updateMemberList() {
        console.log('[Telegram 最后发言时间] 开始更新成员列表...');
        // 这是根据您新提供的成员列表HTML更新的选择器
        const memberList = document.querySelector('.search-super-content-members .chatlist');
        if (!memberList) {
            console.log('[Telegram 最后发言时间] 未找到成员列表容器 (ul.chatlist)');
            return;
        }

        const members = memberList.querySelectorAll('a.chatlist-chat');
        console.log(`[Telegram 最后发言时间] 找到了 ${members.length} 位成员`);

        members.forEach(memberEl => {
            const usernameEl = memberEl.querySelector('.peer-title');
            if (!usernameEl) return;

            const username = usernameEl.innerText.trim();
            const subtitleEl = memberEl.querySelector('.row-subtitle');
            if (!subtitleEl) return;

            // 为我们的状态信息创建一个唯一的容器，以便以后可以轻松找到并更新它
            let statusSpan = subtitleEl.querySelector('.last-seen-status');
            if (!statusSpan) {
                subtitleEl.innerHTML = ''; // 清空原有内容 (如 "在线")
                statusSpan = document.createElement('span');
                statusSpan.className = 'last-seen-status';
                subtitleEl.appendChild(statusSpan);
            }

            let statusIcon = '';
            let statusText = '';
            let statusColor = '';

            if (lastSeen[username]) {
                const userLastSeen = lastSeen[username];
                const now = new Date();
                const diffMinutes = (now - userLastSeen) / (1000 * 60);

                const timeString = userLastSeen.toLocaleString('zh-CN', {dateStyle: 'short', timeStyle: 'short'});

                if (diffMinutes <= 60) {
                    statusIcon = '✅';
                    statusText = ` ${timeString}`;
                    statusColor = '#24a148'; // green
                } else {
                    statusIcon = '❌';
                    statusText = ` ${timeString}`;
                    statusColor = '#da1e28'; // red
                }
            } else {
                statusIcon = '❌';
                statusText = ' 未见发言';
                statusColor = '#8d8d8d'; // grey
            }

            statusSpan.innerHTML = `<span style="color: ${statusColor};">${statusIcon}${statusText}</span>`;
        });
        console.log('[Telegram 最后发言时间] 成员列表更新完毕');
    }

    function run() {
        processMessages();
        updateMemberList();
    }

    let debounceTimer;
    const observer = new MutationObserver(() => {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => {
             // 检查成员列表是否存在，如果存在才运行
             if (document.querySelector('.search-super-content-members .chatlist')) {
                 console.log('[Telegram 最后发言时间] 检测到DOM变化，重新运行脚本');
                 run();
             }
        }, 1000);
    });

    setTimeout(() => {
        const targetNode = document.body;
        if (targetNode) {
            observer.observe(targetNode, { childList: true, subtree: true });
            console.log('[Telegram 最后发言时间] MutationObserver 已启动，监视 document.body');
            run();
        } else {
            console.error('[Telegram 最后发言时间] 无法找到要监视的目标节点 document.body');
        }
    }, 3000);

})(); 