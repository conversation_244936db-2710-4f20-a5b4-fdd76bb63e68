(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([[3185], {
    73450: function(e, t, n) {
        Promise.resolve().then(n.bind(n, 88072))
    },
    13883: function(e, t, n) {
        "use strict";
        n.d(t, {
            kr: function() {
                return s.createContext
            },
            qp: function() {
                return s.useContext
            }
        });
        var s = n(2265)
    },
    99064: function(e, t, n) {
        "use strict";
        n.d(t, {
            X$: function() {
                return s.memo
            }
        });
        var s = n(2265)
    },
    589: function(e, t, n) {
        "use strict";
        n.d(t, {
            X$: function() {
                return s.memo
            },
            d4: function() {
                return s.useEffect
            }
        });
        var s = n(2265)
    },
    3918: function(e, t, n) {
        "use strict";
        var s = n(22599);
        n.o(s, "trim") && n.d(t, {
            trim: function() {
                return s.trim
            }
        })
    },
    66807: function(e, t, n) {
        "use strict";
        n.d(t, {
            I4: function() {
                return s.useCallback
            },
            d4: function() {
                return s.useEffect
            },
            eJ: function() {
                return s.useState
            },
            mT: function() {
                return s.useRef
            }
        });
        var s = n(2265)
    },
    15166: function(e, t, n) {
        "use strict";
        n.d(t, {
            Ye: function() {
                return s.useMemo
            },
            d4: function() {
                return s.useEffect
            },
            mT: function() {
                return s.useRef
            }
        });
        var s = n(2265)
    },
    72585: function(e, t, n) {
        "use strict";
        n.d(t, {
            Ye: function() {
                return s.useMemo
            },
            eJ: function() {
                return s.useState
            }
        });
        var s = n(2265)
    },
    88072: function(e, t, n) {
        "use strict";
        n.r(t),
        n.d(t, {
            default: function() {
                return Tn
            }
        });
        var s, i = n(57437), a = n(73706);
        !function(e) {
            e[e.Ongoing = 0] = "Ongoing",
            e[e.UnStart = 1] = "UnStart",
            e[e.Ended = 2] = "Ended",
            e[e.Invalid = 3] = "Invalid"
        }(s || (s = {}));
        var r = n(2265)
          , o = n(65975)
          , l = n(1055)
          , c = n(95791)
          , d = n(25125)
          , u = n(97595)
          , x = n(68522)
          , p = n(24226)
          , m = n(63288);
        const h = [{
            id: 1,
            name: "Beramas Wonderland",
            description: "",
            path: "/activity/christmas",
            status: s.Ended,
            api: "/api/mas",
            bg: "linear-gradient(180deg, #000 0%, #455972 30%)",
            bgPathname: ["/", "/activity/christmas"],
            handleTime: e=>{
                if (!e)
                    return {};
                let {start_time: t, end_time: n} = e;
                return t && n ? (t *= 1e3,
                n *= 1e3,
                {
                    startUTCTime: (0,
                    l.kj)(t),
                    endUTCTime: (0,
                    l.kj)(n)
                }) : {}
            }
        }];
        const f = {
            currentSceneInfoValid: !1,
            currentSceneInfoLoading: !0,
            list: h,
            current: void 0,
            currentDateTime: new Date
        }
          , g = (0,
        a.kr)(f);
        var v = function(e) {
            let {children: t} = e;
            const n = function() {
                const e = (0,
                r.useRef)()
                  , [t] = (0,
                r.useState)(h)
                  , [n] = (0,
                r.useState)()
                  , [i,a] = (0,
                r.useState)()
                  , [f,g] = (0,
                r.useState)(new Date)
                  , [v,b] = (0,
                r.useState)()
                  , [j,w] = (0,
                r.useState)()
                  , [C,y] = (0,
                r.useState)(!0)
                  , [N,k] = (0,
                r.useState)()
                  , _ = (0,
                r.useMemo)((()=>!!i && i.status === s.Ongoing), [i, C])
                  , Z = (0,
                r.useCallback)((async()=>{
                    var e;
                    let t, n = (new Date).getTime();
                    try {
                        t = await (0,
                        o.U2)("/api/timestamp")
                    } catch (p) {}
                    var s;
                    0 === (null == t ? void 0 : t.code) && (null == t || null === (e = t.data) || void 0 === e ? void 0 : e.timestamp) && (n = 1e3 * (null === (s = t.data) || void 0 === s ? void 0 : s.timestamp));
                    const i = new Date(n);
                    g(i);
                    const a = (0,
                    l.Xx)(n)
                      , r = (0,
                    l.kj)(n);
                    w(r);
                    const x = c.O(d.H(u.B(a, 0), 0), 0).getTime();
                    return b(x),
                    {
                        currentDateTime: i,
                        currentUTCString: r,
                        currentUTCZeroTimestamp: x
                    }
                }
                ), [])
                  , F = (0,
                r.useCallback)((async e=>{
                    if (n && n.api && n.handleTime) {
                        try {
                            const t = await (0,
                            o.U2)(n.api);
                            if (0 !== t.code)
                                return void y(!1);
                            const {startUTCTime: i, endUTCTime: r} = n.handleTime(t.data);
                            if (t.data.startUTCTime = i,
                            t.data.endUTCTime = r,
                            t.data.status = s.Invalid,
                            !i || !r)
                                return y(!1),
                                void a(t.data);
                            t.data.status = s.Ongoing,
                            x.R(new Date(e.currentUTCString), new Date(i)) && (t.data.status = s.UnStart),
                            (p.A(new Date(e.currentUTCString), new Date(r)) || m.X(new Date(e.currentUTCString), new Date(r))) && (t.data.status = s.Ended),
                            a(t.data)
                        } catch (t) {}
                        y(!1)
                    } else
                        y(!1)
                }
                ), []);
                return (0,
                r.useEffect)((()=>{}
                ), []),
                (0,
                r.useEffect)((()=>{
                    if (!j || !i)
                        return;
                    let t = new Date(j);
                    (()=>{
                        try {
                            const e = (0,
                            l.x7)(t, new Date(i.endUTCTime));
                            k(e)
                        } catch (n) {
                            k("")
                        }
                        m.X(t, new Date(i.endUTCTime)) && Z().then((e=>{
                            F(e)
                        }
                        )),
                        p.A(t, new Date(i.endUTCTime)) && clearInterval(e.current)
                    }
                    )()
                }
                ), [j, i]),
                {
                    currentSceneInfoLoading: C,
                    currentSceneInfoValid: _,
                    list: t,
                    current: n,
                    currentSceneInfo: i,
                    currentDateTime: f,
                    currentUTCZeroTimestamp: v,
                    currentUTCString: j,
                    currentRemainingDatetime: N
                }
            }();
            return (0,
            i.jsx)(g.Provider, {
                value: {
                    ...n
                },
                children: t
            })
        }
          , b = n(89566)
          , j = n(75332)
          , w = n(62536)
          , C = n(64218)
          , y = n(43216)
          , N = n(68787)
          , k = n(4394);
        const _ = "78ffdfb23c108ec4853e73cd515afa29";
        const Z = "MonadX"
          , F = "MonadX"
          , A = "https://www.nadsa.space"
          , D = ["/favicon.ico"]
          , T = (Object.values(C.ZP),
        (0,
        y.vX)({
            appName: Z,
            appDescription: F,
            appUrl: A,
            appIcon: D[0],
            storage: (0,
            b.o6)({
                storage: j.Dr
            }),
            ssr: !0,
            projectId: _,
            chains: [k.t],
            transports: {
                [N.rC]: (0,
                w.d)("https://testnet-rpc.monad.xyz")
            }
        }));
        var S = n(66094)
          , L = n(93191)
          , I = n(40181)
          , B = n(20488);
        n(34803);
        const E = new S.S;
        var M = function(e) {
            let {children: t, cookies: n} = e;
            const s = (0,
            j.jx)(T, n);
            return (0,
            i.jsx)(I.F, {
                config: T,
                initialState: s,
                children: (0,
                i.jsx)(L.aH, {
                    client: E,
                    children: (0,
                    i.jsx)(y.pj, {
                        locale: "en-US",
                        initialChain: N.rC,
                        theme: (0,
                        B.$)(),
                        modalSize: "compact",
                        children: t
                    })
                })
            })
        }
          , U = n(13807)
          , O = n(66807)
          , V = n(25177);
        function H() {
            const [e,t] = (0,
            O.eJ)(!1)
              , [n,s] = (0,
            O.eJ)("")
              , i = (0,
            O.mT)()
              , {muted: a} = (0,
            V.w)((e=>e))
              , r = (0,
            O.I4)((()=>{
                i.current && (i.current.pause(),
                i.current = null,
                t(!1),
                s(""))
            }
            ), [])
              , o = (0,
            O.I4)((async a=>{
                if (!V.w.getState().muted)
                    try {
                        if (i.current && n === a)
                            return void (e ? r() : (await i.current.play(),
                            t(!0)));
                        i.current && (r(),
                        await new Promise((e=>setTimeout(e, 50))));
                        const o = new Audio(a);
                        o.preload = "auto",
                        i.current = o,
                        await new Promise(((e,t)=>{
                            o.addEventListener("canplaythrough", e, {
                                once: !0
                            }),
                            o.addEventListener("error", t, {
                                once: !0
                            })
                        }
                        ));
                        V.w.getState().muted || (await o.play(),
                        s(a),
                        t(!0)),
                        o.onended = ()=>{
                            t(!1),
                            i.current = null,
                            s("")
                        }
                    } catch (o) {
                        r()
                    }
            }
            ), [e, n, r]);
            return (0,
            O.d4)((()=>{
                a && e && r()
            }
            ), [a, e, r]),
            (0,
            O.d4)((()=>()=>{
                r()
            }
            ), [r]),
            {
                playing: e,
                playingUrl: n,
                play: o,
                pause: r
            }
        }
        var P = n(83196);
        var R = e=>t=>{
            const {play: n} = H();
            return (0,
            P.d4)((()=>{
                const e = e=>{
                    const t = e.target.closest("[data-click-sound]");
                    if (t) {
                        const e = t.getAttribute("data-click-sound");
                        n(e && "true" !== e ? e : "/audios/press_button.mp3")
                    }
                }
                  , t = e=>{
                    const t = e.target.closest("[data-hover-sound]");
                    if (t) {
                        const e = t.getAttribute("data-hover-sound");
                        n(e && "true" !== e ? e : "/audios/hover.mp3")
                    }
                }
                ;
                return document.addEventListener("click", e),
                document.addEventListener("mouseover", t),
                ()=>{
                    document.removeEventListener("click", e),
                    document.removeEventListener("mouseover", t)
                }
            }
            ), []),
            (0,
            P.d4)((()=>{
                new Audio("/audios/press_button.mp3").preload = "auto"
            }
            ), []),
            (0,
            i.jsx)(e, {
                ...t
            })
        }
          , z = n(43249)
          , X = n(43477)
          , W = n(59698);
        var J = function() {
            let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 1440
              , t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 14.4;
            const n = ()=>{
                const n = window.innerWidth / e;
                document.documentElement.style.setProperty("--rem", "".concat(t * n, "px"))
            }
              , {run: s} = (0,
            X.Z)(n, {
                wait: 100
            });
            (0,
            P.d4)((()=>(n(),
            ()=>{
                document.documentElement.style.setProperty("--rem", "".concat(t, "px"))
            }
            )), []),
            (0,
            W.Z)("resize", s, {
                target: window
            })
        }
          , $ = n(9062)
          , Y = n(89755);
        var q = n(81062)
          , G = n(87138)
          , K = n(99064)
          , Q = n(89483)
          , ee = n(89183)
          , te = n(48101)
          , ne = n(5927);
        function se() {
            const e = (0,
            Q._)(['\n  font-family: Montserrat;\n  background: linear-gradient(180deg, #9892C0 0%, #47445A 100%);\n  &::after {\n    content: "";\n    position: absolute;\n    top: 1px; \n    left: 1px;\n    right: 1px;\n    bottom: 1px;\n    background-color: #2B294A;\n    border-radius: 16px; \n    z-index: 1;\n  }\n']);
            return se = function() {
                return e
            }
            ,
            e
        }
        function ie() {
            const e = (0,
            Q._)(["\n  color: #9290B1;\n  font-family: Unbounded;\n  font-size: 12px;\n  font-style: normal;\n  font-weight: 300;\n  line-height: 150%;\n  margin-top: 14px;\n  text-align: left;\n"]);
            return ie = function() {
                return e
            }
            ,
            e
        }
        function ae() {
            const e = (0,
            Q._)(["\n  display: flex;\n  flex-direction: column;\n  align-items: stretch;\n  gap: 10px;\n  margin-top: 20px;\n"]);
            return ae = function() {
                return e
            }
            ,
            e
        }
        function re() {
            const e = (0,
            Q._)(["\n  border-radius: 6px;\n  background: rgba(255, 255, 255, 0.05);\n  cursor: pointer;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 17px;\n  color: #fff;\n  text-align: center;\n  font-size: 14px;\n  font-family: Unbounded;\n  font-style: normal;\n  font-weight: 400;\n  line-height: 150%;\n"]);
            return re = function() {
                return e
            }
            ,
            e
        }
        function oe() {
            const e = (0,
            Q._)(['\n  width: 16px;\n  height: 16px;\n  border-radius: 50%;\n  border: 1px solid rgba(255, 255, 255, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n\n  &::after {\n    content: "";\n    display: ', ";\n    width: 12px;\n    height: 12px;\n    flex-shrink: 0;\n    border-radius: 12px;\n    background: var(--primary);\n  }\n"]);
            return oe = function() {
                return e
            }
            ,
            e
        }
        function le() {
            const e = (0,
            Q._)(["\n  display: flex;\n  align-items: center;\n  gap: 7px;\n  color: ", ';\n  font-size: 12px;\n  font-style: normal;\n  font-family: Unbounded;\n  font-weight: 300;\n  line-height: normal;\n\n  &::after {\n    content: "";\n    display: block;\n    width: 6px;\n    height: 6px;\n    border-radius: 12px;\n    background: ', ";\n  }\n"]);
            return le = function() {
                return e
            }
            ,
            e
        }
        function ce() {
            const e = (0,
            Q._)(["\n  color: #000;\n  text-align: center;\n  font-family: Montserrat;\n  font-size: 16px;\n  font-style: normal;\n  font-weight: 400;\n  line-height: 150%;\n\n  > p {\n    margin: 0;\n  }\n"]);
            return ce = function() {
                return e
            }
            ,
            e
        }
        const de = ee.ZP.div(se())
          , ue = ee.ZP.div(ie())
          , xe = ee.ZP.div(ae())
          , pe = ee.ZP.div(re())
          , me = ee.ZP.div(oe(), (e=>{
            let {$selected: t} = e;
            return t ? "block" : "none"
        }
        ))
          , he = ee.ZP.div(le(), (e=>{
            let {$color: t} = e;
            return t || "#57DB64"
        }
        ), (e=>{
            let {$color: t} = e;
            return t || "#57DB64"
        }
        ))
          , fe = ee.ZP.div(ce());
        var ge = n(78515);
        var ve = (0,
        K.X$)((e=>{
            const {visible: t} = e
              , n = (0,
            ge.u)()
              , s = ()=>{
                n.setAlert(!1)
            }
            ;
            return (0,
            i.jsx)(te.Z, {
                open: t,
                onClose: s,
                closeIconClassName: "right-[-14px] top-[-8px]",
                children: (0,
                i.jsxs)("div", {
                    className: "flex flex-col items-center gap-[22px] bg-[#FFFDEB] rounded-[8px] p-[20px]",
                    children: [(0,
                    i.jsx)("svg", {
                        width: "38",
                        height: "34",
                        viewBox: "0 0 38 34",
                        fill: "none",
                        xmlns: "http://www.w3.org/2000/svg",
                        children: (0,
                        i.jsx)("path", {
                            fillRule: "evenodd",
                            clipRule: "evenodd",
                            d: "M16.9597 1.28906C17.7295 -0.0442715 19.654 -0.0442702 20.4238 1.28906L37.1125 30.1948C37.8823 31.5281 36.9201 33.1948 35.3805 33.1948H2.00298C0.463382 33.1948 -0.498867 31.5281 0.270933 30.1948L16.9597 1.28906ZM16.9018 14.3999C16.9018 13.4113 17.7033 12.6099 18.6919 12.6099C19.6805 12.6099 20.4819 13.4113 20.4819 14.3999V21.5601C20.4819 22.5487 19.6805 23.3501 18.6919 23.3501C17.7033 23.3501 16.9018 22.5487 16.9018 21.5601V14.3999ZM18.6919 25.1397C17.7033 25.1397 16.9018 25.9411 16.9018 26.9297C16.9018 27.9183 17.7033 28.7197 18.6919 28.7197C19.6805 28.7197 20.4819 27.9183 20.4819 26.9297C20.4819 25.9411 19.6805 25.1397 18.6919 25.1397Z",
                            fill: "#FF547D"
                        })
                    }), (0,
                    i.jsxs)(fe, {
                        children: [(0,
                        i.jsx)("p", {
                            children: "RPC Error!"
                        }), (0,
                        i.jsx)("p", {
                            children: "Click to switch to another node"
                        })]
                    }), (0,
                    i.jsx)(ne.Z, {
                        className: "w-[200px] h-[50px]",
                        type: "primary",
                        onClick: ()=>{
                            n.setVisible(!0),
                            s()
                        }
                        ,
                        children: "Switch"
                    })]
                })
            })
        }
        ))
          , be = n(589)
          , je = n(92030)
          , we = n(40063);
        async function Ce(e, t) {
            const n = (new Date).getTime()
              , s = new je.r(e)
              , i = new Promise(((e,t)=>{
                setTimeout((()=>{
                    t(-1)
                }
                ), we.Bs)
            }
            ));
            return new Promise((e=>{
                Promise.race(t ? [s.getNetwork()] : [s.getNetwork(), i]).then((()=>{
                    const t = (new Date).getTime();
                    e(t - n)
                }
                )).catch((t=>{
                    e(-1)
                }
                ))
            }
            ))
        }
        function ye(e) {
            return e ? e < 0 ? "off" : e + "ms" : "-ms"
        }
        function Ne(e) {
            return e ? e < we.m9.FAST.lt && e >= we.m9.FAST.gte ? we.m9.FAST : e < we.m9.SLOW.lt && e >= we.m9.SLOW.gte ? we.m9.SLOW : we.m9.STOP : we.m9.FAST
        }
        var ke = n(72585);
        const _e = Object.values(we.mV)
          , Ze = Object.keys(we.mV);
        function Fe() {
            const e = (0,
            ge.u)()
              , [t,n] = (0,
            ke.eJ)(!1)
              , s = (0,
            ke.Ye)((()=>e.ping), [e.ping]);
            return {
                ping: (0,
                ke.Ye)((()=>e.ping[e.selected]), [e.ping, e.selected]),
                pingList: s,
                loading: t,
                getCurrentPing: async()=>{
                    var t;
                    const n = await Ce(null === (t = we.mV[e.selected]) || void 0 === t ? void 0 : t.url, !0);
                    return e.setPing({
                        [e.selected]: n
                    }),
                    e.setAlert(n < 0),
                    n
                }
                ,
                getPingList: ()=>{
                    n(!0);
                    const t = [];
                    for (let n = 0; n < _e.length; n++)
                        t.push(new Promise((t=>{
                            Ce(_e[n].url).then((s=>{
                                e.setPing({
                                    [Ze[n]]: s
                                }),
                                t({
                                    [Ze[n]]: s
                                }),
                                e.selected === Ze[n] && s < 0 && e.setAlert(!0)
                            }
                            ))
                        }
                        )));
                    Promise.all(t).then((()=>{
                        n(!1)
                    }
                    ))
                }
            }
        }
        var Ae = n(37399);
        var De = (0,
        be.X$)((e=>{
            const t = Object.values(we.mV)
              , n = Object.keys(we.mV)
              , s = (0,
            ge.u)()
              , a = (0,
            Ae.Z)()
              , {pingList: r, getPingList: o} = Fe();
            return (0,
            be.d4)((()=>{
                o()
            }
            ), []),
            (0,
            i.jsx)(de, {
                className: "bg-[#2B294A] lg:rounded-[16px] md:rounded-t-[16px] md:w-full lg:w-[389px] relative p-[1px]",
                children: (0,
                i.jsxs)("div", {
                    className: "relative z-[2] p-4",
                    children: [(0,
                    i.jsxs)("div", {
                        className: "flex items-center justify-between",
                        children: [(0,
                        i.jsx)("div", {
                            className: "text-[18px] text-white font-[400] font-Unbounded leading-[1]",
                            children: "RPC Selector"
                        }), (0,
                        i.jsx)("svg", {
                            onClick: ()=>{
                                s.setVisible(!1)
                            }
                            ,
                            className: "cursor-pointer",
                            width: "13",
                            height: "13",
                            viewBox: "0 0 13 13",
                            fill: "none",
                            xmlns: "http://www.w3.org/2000/svg",
                            children: (0,
                            i.jsx)("path", {
                                "fill-rule": "evenodd",
                                "clip-rule": "evenodd",
                                d: "M6.01041 4.59619L1.41422 0L0 1.41418L4.59621 6.01038L0 10.6066L1.41422 12.0208L6.01041 7.42462L10.6066 12.0208L12.0208 10.6066L7.42461 6.01038L12.0208 1.41418L10.6066 0L6.01041 4.59619Z",
                                fill: "#A6A6DB"
                            })
                        })]
                    }), (0,
                    i.jsx)(ue, {
                        children: "Select the available RPC service below and the page will be automatically refreshed"
                    }), (0,
                    i.jsx)(xe, {
                        children: t.map(((e,t)=>(0,
                        i.jsxs)(pe, {
                            onClick: ()=>(e=>{
                                e !== s.selected && (s.setSelected(e),
                                s.setVisible(!1),
                                navigator.clipboard.writeText(we.mV[e].url),
                                a.success({
                                    title: "Copied rpc ".concat(we.mV[e].simpleName)
                                }),
                                setTimeout((()=>{
                                    window.history.go(0)
                                }
                                ), 1e3))
                            }
                            )(n[t]),
                            children: [e.simpleName, (0,
                            i.jsxs)("div", {
                                className: "flex gap-[10px] items-center",
                                children: [(0,
                                i.jsx)(he, {
                                    $color: Ne(r[n[t]]).color,
                                    children: ye(r[n[t]])
                                }), (0,
                                i.jsx)(me, {
                                    $selected: s.selected === n[t]
                                })]
                            })]
                        }, t)))
                    })]
                })
            })
        }
        ));
        var Te = (0,
        K.X$)((e=>{
            const {visible: t, onClose: n} = e;
            return (0,
            i.jsx)(te.Z, {
                open: t,
                onClose: n,
                isShowCloseIcon: !1,
                children: (0,
                i.jsx)(De, {})
            })
        }
        ))
          , Se = n(7781)
          , Le = n(92115);
        function Ie() {
            const e = (0,
            Q._)(["\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  color: ", ";\n  font-size: 10px;\n  font-style: normal;\n  font-weight: 400;\n  line-height: normal;\n  cursor: pointer;\n  transition: opacity 0.2s linear;\n  border-radius: 18px;\n  background: rgba(62, 52, 124, 0.60);\n  padding: 4px 10px;\n\n  backdrop-filter: blur(5px);\n\n  &:hover {\n    opacity: 1;\n  }\n\n  ", "\n"]);
            return Ie = function() {
                return e
            }
            ,
            e
        }
        const Be = ee.ZP.div(Ie(), (e=>{
            let {$color: t} = e;
            return t || "#57DB64"
        }
        ), (e=>{
            let {isMobile: t, $color: n} = e;
            return t ? '\n    &&::before {\n      content: "";\n      display: block;\n      width: 6px;\n      height: 6px;\n      flex-shrink: 0;\n      background: '.concat(n || "#57DB64", ";\n      border-radius: 50%;\n      margin-right: 5px;\n    }\n  ") : '\n    &&::after {\n      content: "";\n      display: block;\n      width: 6px;\n      height: 6px;\n      flex-shrink: 0;\n      background: '.concat(n || "#57DB64", ";\n      border-radius: 50%;\n    }\n  ")
        }
        ));
        var Ee = e=>{
            let {className: t} = e;
            const n = (0,
            ge.u)()
              , {account: s} = (0,
            Se.Z)()
              , {ping: a, getCurrentPing: r} = Fe()
              , o = (0,
            Le.Z)();
            return (0,
            P.d4)((()=>{
                r()
            }
            ), []),
            (0,
            i.jsxs)("div", {
                children: [(0,
                i.jsx)(Be, {
                    $color: Ne(a).color,
                    "data-bp": "1001-008",
                    onClick: ()=>{
                        n.setVisible(!0)
                    }
                    ,
                    isMobile: o,
                    className: t,
                    children: ye(a)
                }), (0,
                i.jsx)(Te, {
                    visible: n.visible,
                    onClose: ()=>{
                        n.setVisible(!1)
                    }
                }), (0,
                i.jsx)(ve, {
                    visible: n.alert
                })]
            })
        }
          , Me = n(96509)
          , Ue = (0,
        r.memo)((function() {
            const e = (0,
            V.w)()
              , t = (0,
            r.useRef)();
            return (0,
            r.useEffect)((()=>{
                e.set({
                    conveyorBeltRef: t
                })
            }
            ), []),
            (0,
            r.useEffect)((()=>{
                t.current && t.current.muted(null == e ? void 0 : e.muted)
            }
            ), [null == e ? void 0 : e.muted]),
            (0,
            i.jsxs)(i.Fragment, {
                children: [(0,
                i.jsx)("div", {
                    className: "w-[24px] cursor-pointer",
                    onClick: ()=>{
                        e.set({
                            muted: !(null == e ? void 0 : e.muted)
                        })
                    }
                    ,
                    children: (null == e ? void 0 : e.muted) ? (0,
                    i.jsx)("img", {
                        src: "/images/close_audio.svg",
                        alt: "close_audio"
                    }) : (0,
                    i.jsx)("img", {
                        src: "/images/open_audio.svg",
                        alt: "open_audio"
                    })
                }), (0,
                i.jsx)(Me.Z, {
                    src: "/audios/dapps/conveyor_belt.mp3",
                    ref: t,
                    config: {
                        loop: !0
                    }
                })]
            })
        }
        ))
          , Oe = n(54887)
          , Ve = n(23266)
          , He = n(34446)
          , Pe = n(88469);
        var Re, ze = e=>{
            const {visible: t, style: n, className: s, overlayStyle: a, overlayClassName: r, children: o, direction: l=0, size: c, onClose: d} = e
              , u = (0,
            Ve.mT)(null)
              , x = function(e, t) {
                if ([0, 3].includes(e))
                    return t ? {
                        height: t
                    } : {
                        height: "80vh"
                    };
                if (!t)
                    return {
                        width: "80vw"
                    };
                return {
                    width: t
                }
            }(l, c)
              , p = function(e, t) {
                const n = {
                    visible: {},
                    hidden: {}
                };
                switch (e) {
                case 0:
                    n.visible = {
                        y: 0
                    },
                    n.hidden = {
                        y: t.height
                    };
                    break;
                case 1:
                    n.visible = {
                        x: 0
                    },
                    n.hidden = {
                        x: "-".concat(t.width)
                    };
                    break;
                case 2:
                    n.visible = {
                        x: 0
                    },
                    n.hidden = {
                        x: t.width
                    };
                    break;
                case 3:
                    n.visible = {
                        y: 0
                    },
                    n.hidden = {
                        y: "-".concat(t.height)
                    }
                }
                return n
            }(l, x)
              , [m,h] = (0,
            Ve.eJ)(t)
              , {run: f} = (0,
            X.Z)((()=>{
                null == d || d()
            }
            ), {
                wait: 300
            });
            return (0,
            Ve.d4)((()=>{
                h(t)
            }
            ), [t]),
            "undefined" == typeof document ? null : Oe.createPortal((0,
            i.jsx)(He.M, {
                mode: "wait",
                children: t && (0,
                i.jsx)(Pe.E.div, {
                    className: "fixed inset-0 bg-black bg-opacity-50 z-50 ".concat(r),
                    style: a,
                    onClick: e=>{
                        u.current.contains(e.target) || (h(!1),
                        f())
                    }
                    ,
                    initial: {
                        opacity: 0
                    },
                    animate: {
                        opacity: 1
                    },
                    exit: {
                        opacity: 0
                    },
                    children: (0,
                    i.jsx)(Pe.E.div, {
                        ref: u,
                        className: "fixed z-50 ".concat(s),
                        style: {
                            ...n,
                            ...Xe[l],
                            ...x
                        },
                        initial: p.hidden,
                        animate: p.visible,
                        exit: p.hidden,
                        children: o
                    })
                })
            }), document.body)
        }
        ;
        !function(e) {
            e[e.Bottom = 0] = "Bottom",
            e[e.Left = 1] = "Left",
            e[e.Right = 2] = "Right",
            e[e.Top = 3] = "Top"
        }(Re || (Re = {}));
        const Xe = {
            0: {
                left: 0,
                bottom: 0,
                width: "100%",
                borderTopLeftRadius: 20,
                borderTopRightRadius: 20
            },
            1: {
                left: 0,
                top: 0,
                height: "100%",
                borderBottomRightRadius: 20,
                borderTopRightRadius: 20
            },
            2: {
                right: 0,
                top: 0,
                height: "100%",
                borderBottomLeftRadius: 20,
                borderTopLeftRadius: 20
            },
            3: {
                left: 0,
                top: 0,
                width: "100%",
                borderBottomLeftRadius: 20,
                borderBottomRightRadius: 20
            }
        };
        var We = n(74703);
        const Je = e=>{
            let {setMobileUserInfoVisible: t} = e;
            const {disconnect: n} = (0,
            We.q)();
            return (0,
            i.jsx)("div", {
                className: "cursor-pointer flex gap-2 items-center click transition-all duration-300",
                onClick: ()=>{
                    n(),
                    t(!1)
                }
                ,
                children: (0,
                i.jsx)("svg", {
                    width: "16",
                    height: "16",
                    viewBox: "0 0 16 16",
                    fill: "none",
                    xmlns: "http://www.w3.org/2000/svg",
                    children: (0,
                    i.jsx)("path", {
                        "fill-rule": "evenodd",
                        "clip-rule": "evenodd",
                        d: "M1.91613 16H10.5731C11.0656 16 11.4652 15.57 11.4652 15.04C11.4652 14.51 11.0656 14.08 10.5731 14.08H2.00906C1.92728 13.974 1.78417 13.662 1.78417 13.164V2.838C1.78417 2.34 1.92728 2.028 2.00906 1.92H10.5731C11.0656 1.92 11.4652 1.49 11.4652 0.96C11.4652 0.43 11.0656 0 10.5731 0H1.91613C0.823322 0 0 1.22 0 2.838V13.162C0 14.78 0.823322 16 1.91613 16ZM12.3929 12.2771L15.7266 8.69156L15.7383 8.67941C15.913 8.49136 16.0004 8.24579 16.0003 8.00023C16.0003 7.75467 15.913 7.5091 15.7383 7.32106L15.7237 7.30575L12.3948 3.72341C12.0454 3.34741 11.4823 3.34741 11.1329 3.72341C10.7835 4.09941 10.7835 4.70541 11.1329 5.08141L12.953 7.03906H6.83918C6.34667 7.03906 5.94709 7.46906 5.94709 7.99906C5.94709 8.52906 6.34667 8.95906 6.83918 8.95906H12.9542L11.1329 10.9191C10.7835 11.2951 10.7835 11.9011 11.1329 12.2771C11.3057 12.4651 11.5343 12.5591 11.7629 12.5591C11.9915 12.5591 12.2201 12.4651 12.3929 12.2771Z",
                        fill: "#FF689A"
                    })
                })
            })
        }
        ;
        var $e = e=>{
            const {visible: t, onClose: n, setMobileUserInfoVisible: s, walletInfo: a, handleCopy: r, address: o, tokenLogoShown: l, balanceShown: c, tokenSymbolShown: d, chainId: u, userInfo: x, currentChainInfo: p} = e
              , m = (null == a ? void 0 : a.name) || "";
            return (0,
            i.jsx)(ze, {
                visible: t,
                onClose: n,
                size: "406px",
                className: "bg-[url(/images/mobile/modal-account.svg)] bg-no-repeat bg-cover",
                children: (0,
                i.jsxs)("div", {
                    className: "mt-[72px] px-[30px]",
                    children: [(0,
                    i.jsx)("div", {
                        className: "flex gap-2 items-center",
                        children: (0,
                        i.jsxs)("div", {
                            className: "flex-1 flex flex-col gap-2",
                            children: [(0,
                            i.jsxs)("div", {
                                className: "w-full text-[#A6A6DB] text-[12px] font-Unbounded text-nowrap leading-[1] overflow-hidden overflow-ellipsis",
                                children: ["Connected with ", m]
                            }), (0,
                            i.jsxs)("div", {
                                className: "flex items-center justify-between pb-4 border-b border-[#A6A6DB] border-opacity-[0.3]",
                                children: [(0,
                                i.jsx)("div", {
                                    className: "text-white text-[18px] font-Unbounded leading-[1]",
                                    children: o ? "".concat(o.slice(0, 8), "...").concat(o.slice(-6)) : ""
                                }), (0,
                                i.jsxs)("div", {
                                    className: "flex items-center gap-[18px]",
                                    children: [(0,
                                    i.jsx)("div", {
                                        className: "click cursor-pointer",
                                        onClick: r,
                                        children: (0,
                                        i.jsxs)("svg", {
                                            width: "18",
                                            height: "18",
                                            viewBox: "0 0 18 18",
                                            fill: "none",
                                            xmlns: "http://www.w3.org/2000/svg",
                                            children: [(0,
                                            i.jsx)("rect", {
                                                x: "0.99707",
                                                y: "5.26526",
                                                width: "11.0018",
                                                height: "11.735",
                                                rx: "2",
                                                stroke: "#A6A6DB",
                                                "stroke-width": "1.6"
                                            }), (0,
                                            i.jsx)("path", {
                                                d: "M5.9978 3.66705V3C5.9978 1.89543 6.89323 1 7.9978 1H14.9996C16.1042 1 16.9996 1.89543 16.9996 3V10.735C16.9996 11.8396 16.1042 12.735 14.9996 12.735H13.9991",
                                                stroke: "#A6A6DB",
                                                "stroke-width": "1.6"
                                            })]
                                        })
                                    }), (0,
                                    i.jsx)(Je, {
                                        setMobileUserInfoVisible: s
                                    })]
                                })]
                            })]
                        })
                    }), (0,
                    i.jsx)("div", {
                        className: "mt-4 w-full text-[#A6A6DB] text-[12px] font-Unbounded text-nowrap leading-[1]",
                        children: "Network"
                    }), (0,
                    i.jsxs)("div", {
                        className: "mt-2.5 flex items-center gap-2 pb-4 border-b border-[#A6A6DB] border-opacity-[0.3]",
                        children: [(0,
                        i.jsx)("img", {
                            src: "/images/mobile/monad-testnet.svg",
                            alt: ""
                        }), (0,
                        i.jsxs)("div", {
                            className: "text-white font-Unbounded leading-[1] text-[14px]",
                            children: [(null == p ? void 0 : p.id) === k.t.id ? "Monad" : (null == p ? void 0 : p.name) || "", " ", (null == p ? void 0 : p.testnet) ? "Testnet" : "Mainnet"]
                        })]
                    }), (0,
                    i.jsx)("div", {
                        className: "mt-4 w-full text-[#A6A6DB] text-[12px] font-Unbounded text-nowrap leading-[1]",
                        children: "MON Amount"
                    }), (0,
                    i.jsxs)("div", {
                        className: "flex items-center justify-between mt-[10px] pb-4 border-b border-[#A6A6DB] border-opacity-[0.3]",
                        children: [(0,
                        i.jsxs)("div", {
                            className: "flex items-center gap-2",
                            children: [(0,
                            i.jsx)("div", {
                                className: "relative w-[26px] h-[26px] rounded-full shrink-0",
                                style: {
                                    backgroundImage: 'url("'.concat(l, '")'),
                                    backgroundPosition: "center",
                                    backgroundSize: "contain",
                                    backgroundRepeat: "no-repeat"
                                }
                            }), (0,
                            i.jsx)("div", {
                                className: "text-white text-[14px] leading-[14px]",
                                children: d
                            })]
                        }), (0,
                        i.jsx)("div", {
                            className: "text-white text-[16px] font-Unbounded flex-shrink-0 overflow-hidden text-nowrap",
                            children: c
                        })]
                    })]
                })
            })
        }
          , Ye = n(59080)
          , qe = n(93237)
          , Ge = n(75130)
          , Ke = n(60605)
          , Qe = n(68090)
          , et = n(7925)
          , tt = n(52456)
          , nt = n(43298)
          , st = n(15585)
          , it = n(68983)
          , at = n(43507)
          , rt = n(31532)
          , ot = n(13998)
          , lt = n(66920)
          , ct = n(55450)
          , dt = n(40046)
          , ut = n(64489)
          , xt = n(81679);
        const pt = 10143
          , mt = {
            monad: {
                address: "native",
                isNative: !0,
                chainId: pt,
                symbol: "MONAD",
                decimals: 18,
                name: "MONAD",
                icon: "/assets/tokens/monad.svg",
                color: "#78350F"
            },
            meth: {
                chainId: pt,
                address: "******************************************",
                decimals: 18,
                symbol: "METH",
                name: "METH",
                icon: "/assets/tokens/eth.png",
                color: "#D2D2D2"
            },
            weth: {
                chainId: pt,
                address: "******************************************",
                decimals: 18,
                symbol: "WETH",
                name: "Wrapped Ether",
                icon: "/assets/tokens/weth.png",
                color: "#D2D2D2"
            }
        };
        var ht = n(93333);
        const ft = e=>"native" === e.address ? {
            ...e,
            address: "******************************************"
        } : e;
        var gt = {
            80094: Object.values(mt).map(ft),
            1: Object.values(Ge.t).map(ft),
            534352: Object.values(xt.A).map(ft),
            42161: Object.values(Ke.y).map(ft),
            43114: Object.values(Qe.p).map(ft),
            8453: Object.values(et.u).map(ft),
            56: Object.values(tt.e).map(ft).filter((e=>-1 === ["RDNT", "JONES", "BSC-USD", "BTCB"].indexOf(e.symbol))),
            100: Object.values(nt.K).map(ft),
            59144: Object.values(st.P).map(ft),
            169: Object.values(it.f).map(ft),
            5e3: Object.values(at.Z).map(ft),
            1088: Object.values(rt.t).map(ft),
            34443: Object.values(ot.x).map(ft),
            137: Object.values(lt.y).map(ft).filter((e=>-1 === ["WETH"].indexOf(e.symbol))),
            1101: Object.values(qe.n).map(ft),
            324: Object.values(ct.j).map(ft),
            10: Object.values(dt.v).map(ft),
            81457: Object.values(ut.A).map(ft),
            10143: Object.values(mt).map(ft),
            11155111: Object.values(ht.F).map(ft)
        }
          , vt = n(56744)
          , bt = n(61574)
          , jt = n(58789)
          , wt = n(16463)
          , Ct = n(26393)
          , yt = n(26735)
          , Nt = n(87203)
          , kt = n(6793)
          , _t = n(94956);
        const Zt = e=>{
            let {onConnect: t} = e;
            return (0,
            Le.Z)() ? (0,
            i.jsx)("div", {
                onClick: t,
                "data-click-sound": !0,
                "data-bp": "1001-001",
                className: "w-[128px] h-[36px] bg-no-repeat bg-[url(/images/mobile/connect.svg)]"
            }) : (0,
            i.jsx)("div", {
                className: "flex items-center justify-center h-[50px] w-[158px]  bg-[url('/images/header/right_bg.svg')] bg-left bg-contain",
                children: (0,
                i.jsx)("button", {
                    "data-click-sound": !0,
                    "data-bp": "1001-001",
                    className: "w-[122px] h-[34px] bg-[url('/images/header/button_bg.svg')] cursor-pointer font-Unbounded text-[12px] text-[#090909] font-semibold",
                    onClick: t,
                    children: "Connect"
                })
            })
        }
        ;
        var Ft = (0,
        r.memo)((e=>{
            let {className: t} = e;
            const n = (0,
            y.We)()
              , [s,a] = (0,
            r.useState)({})
              , o = ((0,
            wt.usePathname)(),
            (0,
            Le.Z)())
              , l = (0,
            Ae.Z)()
              , {address: c, isConnected: d, chainId: u, chain: x, isConnecting: p} = (0,
            yt.m)()
              , m = (0,
            Nt.K)({
                address: c,
                chainId: N.rC
            })
              , {refetch: h} = m
              , {data: f} = (0,
            kt.O)({
                watch: !0
            })
              , {userInfo: g} = (0,
            q.Z)()
              , v = (0,
            vt.Z)()
              , b = (0,
            _t.Z)()
              , {play: j} = H()
              , w = ()=>{
                j("/audios/press_button.mp3")
            }
              , [C,k] = (0,
            r.useState)(p)
              , [_,Z] = (0,
            r.useState)(!1)
              , [F,A] = (0,
            r.useState)(!1)
              , D = b.chains.find((e=>e.id === u))
              , T = function() {
                var e;
                o && d ? Z(!0) : !c && (null === (e = n.openConnectModal) || void 0 === e || e.call(n))
            }
              , S = (0,
            r.useMemo)((()=>{
                var e;
                if (!c)
                    return "";
                if (null == g || null === (e = g.twitter) || void 0 === e ? void 0 : e.twitter_user_name) {
                    var t;
                    const e = null == g || null === (t = g.twitter) || void 0 === t ? void 0 : t.twitter_user_name;
                    return "".concat(e)
                }
                return "".concat(c.slice(0, 5), "...").concat(c.slice(-4))
            }
            ), [g, c, o])
              , L = ()=>{
                w(),
                navigator.clipboard.writeText(c),
                l.success({
                    title: "Copied address ".concat(c)
                })
            }
              , I = (0,
            r.useMemo)((()=>{
                var e;
                return (null == m || null === (e = m.data) || void 0 === e ? void 0 : e.value) ? (0,
                bt.Z)(jt.bM(m.data.value.toString(), m.data.decimals)).toFixed(3, 0) : "0.000"
            }
            ), [m])
              , B = (0,
            r.useMemo)((()=>{
                var e, t, n;
                return (null == m || null === (e = m.data) || void 0 === e ? void 0 : e.symbol) ? null == m || null === (t = m.data) || void 0 === t ? void 0 : t.symbol : x ? null === (n = x.nativeCurrency) || void 0 === n ? void 0 : n.symbol : ""
            }
            ), [m, x])
              , E = (0,
            r.useMemo)((()=>{
                const e = "/images/monad.svg";
                if (!u)
                    return e;
                const t = gt[u];
                if (!t)
                    return e;
                const n = t.find((e=>e.symbol === B));
                return n ? n.icon : e
            }
            ), [u, B])
              , {run: M, cancel: U} = (0,
            X.Z)((()=>{
                k(!1)
            }
            ), {
                wait: 1e4
            });
            return (0,
            r.useEffect)((()=>{
                U(),
                p ? (k(!0),
                M()) : k(!1)
            }
            ), [p]),
            (0,
            r.useEffect)((()=>{
                h()
            }
            ), [f, h]),
            (0,
            i.jsxs)(i.Fragment, {
                children: [C ? (0,
                i.jsx)(Ct.Z, {
                    width: o ? 128 : 125,
                    height: o ? 36 : 50,
                    borderRadius: 21,
                    style: {
                        transform: "translateY(-4px)"
                    }
                }) : d ? (0,
                i.jsx)("div", {
                    className: "flex items-center justify-center w-[165px] lg:h-[43px] md:h-[36px] lg:bg-[url('/images/header/user_bg.svg')] bg-no-repeat bg-center",
                    children: (0,
                    i.jsx)(At, {
                        handleConnect: T,
                        isMobile: o,
                        address: c,
                        userInfo: g,
                        walletInfo: v,
                        handleCopy: L,
                        tokenLogoShown: E,
                        chainId: u,
                        balanceShown: I,
                        tokenSymbolShown: B,
                        addressShown: S,
                        setMobileUserInfoVisible: Z,
                        currentChainInfo: D,
                        handlePlay: w
                    })
                }) : (0,
                i.jsx)(Zt, {
                    onConnect: T
                }), (0,
                i.jsx)($e, {
                    visible: _,
                    setMobileUserInfoVisible: Z,
                    onClose: ()=>{
                        Z(!1)
                    }
                    ,
                    walletInfo: v,
                    addressShown: S,
                    address: c,
                    tokenLogoShown: E,
                    balanceShown: I,
                    tokenSymbolShown: B,
                    chainId: u,
                    handleDisconnect: ()=>{}
                    ,
                    handleCopy: L,
                    userInfo: g,
                    currentChainInfo: D
                })]
            })
        }
        ));
        const At = e=>{
            var t, n, s, a;
            const {handleConnect: r, isMobile: o, address: l, userInfo: c, walletInfo: d, handleCopy: u, tokenLogoShown: x, chainId: p, balanceShown: m, tokenSymbolShown: h, addressShown: f, setMobileUserInfoVisible: g, currentChainInfo: v, handlePlay: b} = e
              , j = (0,
            wt.useRouter)()
              , w = (0,
            i.jsxs)("div", {
                className: "bg-[url(/images/header/wallet-popover-bg.svg)] w-[205px] h-[218px] overflow-hidden",
                children: [(0,
                i.jsxs)("div", {
                    className: "px-2.5 mt-[28px]",
                    children: [(0,
                    i.jsx)("div", {
                        className: "flex justify-between mb-5",
                        children: (0,
                        i.jsxs)("div", {
                            className: "flex gap-2",
                            children: [(0,
                            i.jsx)(St, {
                                hasAvatar: l && !!(null == c || null === (t = c.twitter) || void 0 === t ? void 0 : t.twitter_avatar),
                                userInfo: c
                            }), (0,
                            i.jsxs)("div", {
                                className: "flex flex-col gap-[6px] justify-center",
                                children: [(0,
                                i.jsx)("div", {
                                    className: "text-white w-[140px] text-[12px] font-[400] leading-[1] font-Unbounded whitespace-nowrap overflow-hidden text-ellipsis",
                                    children: f
                                }), !!(null == c || null === (n = c.twitter) || void 0 === n ? void 0 : n.twitter_user_name) && (0,
                                i.jsxs)("div", {
                                    className: "flex items-center text-white text-[10px] gap-2",
                                    children: [(0,
                                    i.jsx)("div", {
                                        children: "".concat(l.slice(0, 5), "...").concat(l.slice(-4))
                                    }), (0,
                                    i.jsx)("img", {
                                        className: "cursor-pointer",
                                        src: "/images/header/copy.svg",
                                        onClick: u,
                                        alt: ""
                                    })]
                                })]
                            })]
                        })
                    }), (0,
                    i.jsxs)("div", {
                        className: "flex px-[6px] h-[40px] items-center justify-between w-full bg-white bg-opacity-20 rounded-[6px]",
                        children: [(0,
                        i.jsxs)("div", {
                            className: "flex items-center gap-1",
                            children: [(0,
                            i.jsx)("img", {
                                src: x,
                                className: "w-5 h-5",
                                alt: ""
                            }), (0,
                            i.jsx)("div", {
                                className: "text-white text-[12px] font-Unbounded font-[400]",
                                children: h
                            })]
                        }), (0,
                        i.jsx)("div", {
                            className: "text-white text-[12px] font-Unbounded font-[400]",
                            children: m
                        })]
                    }), (0,
                    i.jsxs)("div", {
                        className: "flex items-center gap-1 mt-2 ml-[4px]",
                        children: [(0,
                        i.jsx)("img", {
                            src: "/images/icon-faucet.svg",
                            alt: ""
                        }), (0,
                        i.jsx)("div", {
                            onClick: ()=>{
                                b(),
                                j.push("/faucet")
                            }
                            ,
                            className: "text-[12px] font-[300] leading-[1] font-Unbounded text-[#A6A6DB] underline hover:text-white cursor-pointer",
                            children: "Faucet"
                        })]
                    })]
                }), (0,
                i.jsx)("div", {
                    className: "w-full h-[1px] bg-[#A6A6DB] bg-opacity-10 mt-3"
                }), (0,
                i.jsx)(Dt, {
                    handlePlay: b,
                    setMobileUserInfoVisible: g
                })]
            });
            return (0,
            i.jsx)(Pe.E.div, {
                className: "relative flex justify-center items-center cursor-pointer transition-all duration-300",
                onClick: r,
                whileHover: "active",
                animate: "default",
                initial: "default",
                children: (0,
                i.jsx)(Ye.ZP, {
                    trigger: Ye.xo.Hover,
                    placement: Ye.m9.Bottom,
                    content: o ? null : w,
                    contentStyle: {
                        zIndex: 100
                    },
                    children: o ? (0,
                    i.jsxs)("div", {
                        className: "flex items-center gap-1",
                        onClick: r,
                        children: [(0,
                        i.jsx)(St, {
                            hasAvatar: l && !!(null == c || null === (s = c.twitter) || void 0 === s ? void 0 : s.twitter_avatar),
                            userInfo: c
                        }), (0,
                        i.jsx)("div", {
                            className: "w-[1px] h-[23px] bg-[#A6A6DB] bg-opacity-30 mx-[14px]"
                        }), (0,
                        i.jsx)(Tt, {
                            balanceShown: m
                        })]
                    }) : (0,
                    i.jsxs)("div", {
                        className: "flex items-center gap-1",
                        children: [(0,
                        i.jsx)(Tt, {
                            balanceShown: m
                        }), (0,
                        i.jsx)(St, {
                            hasAvatar: l && !!(null == c || null === (a = c.twitter) || void 0 === a ? void 0 : a.twitter_avatar),
                            userInfo: c
                        })]
                    })
                })
            })
        }
          , Dt = e=>{
            let {setMobileUserInfoVisible: t, handlePlay: n} = e;
            const {disconnect: s} = (0,
            We.q)();
            return (0,
            i.jsxs)("div", {
                className: "cursor-pointer pl-[22px] pr-[26px] flex justify-between items-center click mt-[10px] pt-[10px] pb-[10px] transition-all duration-300 hover:opacity-50",
                "data-click-sound": !0,
                onClick: ()=>{
                    n(),
                    s(),
                    t(!1)
                }
                ,
                children: [(0,
                i.jsx)("div", {
                    className: "text-white font-Unbounded text-[12px] font-[400] leading-[1]",
                    children: "Disconnect"
                }), (0,
                i.jsx)("div", {
                    children: (0,
                    i.jsx)("svg", {
                        xmlns: "http://www.w3.org/2000/svg",
                        width: "16",
                        height: "16",
                        viewBox: "0 0 16 16",
                        fill: "none",
                        children: (0,
                        i.jsx)("path", {
                            fillRule: "evenodd",
                            clipRule: "evenodd",
                            d: "M1.91613 16H10.5731C11.0656 16 11.4652 15.57 11.4652 15.04C11.4652 14.51 11.0656 14.08 10.5731 14.08H2.00906C1.92728 13.974 1.78417 13.662 1.78417 13.164V2.83802C1.78417 2.34002 1.92728 2.02802 2.00906 1.92002H10.5731C11.0656 1.92002 11.4652 1.49002 11.4652 0.960015C11.4652 0.430015 11.0656 1.52588e-05 10.5731 1.52588e-05H1.91613C0.823322 1.52588e-05 0 1.22002 0 2.83802V13.162C0 14.78 0.823322 16 1.91613 16ZM12.3929 12.2771L15.7266 8.69158L15.7383 8.67942C15.913 8.49137 16.0004 8.2458 16.0003 8.00024C16.0003 7.75469 15.913 7.50912 15.7383 7.32108L15.7237 7.30576L12.3948 3.72342C12.0454 3.34742 11.4823 3.34742 11.1329 3.72342C10.7835 4.09942 10.7835 4.70542 11.1329 5.08142L12.953 7.03908H6.83918C6.34667 7.03908 5.94709 7.46908 5.94709 7.99908C5.94709 8.52908 6.34667 8.95908 6.83918 8.95908H12.9542L11.1329 10.9191C10.7835 11.2951 10.7835 11.9011 11.1329 12.2771C11.3057 12.4651 11.5343 12.5591 11.7629 12.5591C11.9915 12.5591 12.2201 12.4651 12.3929 12.2771Z",
                            fill: "#836EF9"
                        })
                    })
                })]
            })
        }
          , Tt = e=>{
            let {className: t="", balanceShown: n} = e;
            return (0,
            i.jsxs)("div", {
                className: "flex items-center gap-1 ".concat(t),
                children: [(0,
                i.jsx)("img", {
                    src: "/images/monad.svg",
                    className: "w-5 h-5",
                    alt: ""
                }), (0,
                i.jsx)("div", {
                    className: "text-[12px] text-white font-[400] font-Unbounded",
                    children: n || "-"
                })]
            })
        }
          , St = e=>{
            let {hasAvatar: t=!1, userInfo: n} = e;
            var s;
            return t ? (0,
            i.jsx)("img", {
                src: null == n || null === (s = n.twitter) || void 0 === s ? void 0 : s.twitter_avatar,
                alt: "",
                className: "w-[28px] h-[28px] rounded-full"
            }) : (0,
            i.jsx)("div", {
                className: "w-[28px] h-[28px] rounded-[50%] border-[2px] border-black bg-[conic-gradient(from_180deg_at_50%_50%,#00D1FF_0deg,#FF008A_360deg)]"
            })
        }
        ;
        var Lt = ()=>(0,
        i.jsx)("div", {
            className: "z-[50] fixed bottom-0 w-full h-[53px] bg-[url(/images/mobile/footer-container.svg)] bg-cover bg-no-repeat flex items-center",
            children: (0,
            i.jsxs)("div", {
                className: "w-full flex justify-between items-center mb-2 px-[6px] relative",
                children: [(0,
                i.jsxs)("div", {
                    className: "flex items-center gap-[10px]",
                    children: [(0,
                    i.jsx)(G.default, {
                        className: "ease-in-out duration-300 w-[24px] h-[24px] cursor-pointer",
                        href: "https://x.com/0xNADSA",
                        target: "_blank",
                        "data-bp": "1001-010",
                        children: (0,
                        i.jsx)("img", {
                            src: "/images/footer/x.svg",
                            alt: "x",
                            className: "w-[24px] cursor-pointer"
                        })
                    }), (0,
                    i.jsx)(G.default, {
                        className: "hover:scale-110 ease-in-out duration-300 w-[24px] h-[24px] cursor-pointer",
                        href: "https://mirror.xyz/0xBd6E844F7DaCAF6339C26D5114F83986914803ef",
                        target: "_blank",
                        "data-bp": "1001-011",
                        children: (0,
                        i.jsx)("img", {
                            src: "/images/footer/mirror.svg",
                            alt: "mirror",
                            className: "w-[24px] cursor-pointer"
                        })
                    }), (0,
                    i.jsx)(G.default, {
                        className: "group  ease-in-out duration-300 w-[24px] cursor-pointer relative",
                        href: "/terminal",
                        target: "_blank",
                        "data-bp": "1001-012",
                        children: (0,
                        i.jsx)("img", {
                            src: "/images/footer/terminal.svg",
                            alt: "ternimal",
                            className: "w-[24px] cursor-pointer"
                        })
                    })]
                }), (0,
                i.jsx)("div", {
                    className: "absolute -top-1 left-1/2 -translate-x-1/2",
                    children: (0,
                    i.jsx)(Ft, {})
                }), (0,
                i.jsx)(Ee, {})]
            })
        })
          , It = (0,
        K.X$)((function() {
            const e = (0,
            Le.Z)();
            return e ? (0,
            i.jsx)(Lt, {}) : (0,
            i.jsxs)("div", {
                className: "z-[99] fixed bottom-0 left-0 flex items-center gap-[10px] pl-[12px] w-[164px] h-[38px]",
                children: [(0,
                i.jsxs)(G.default, {
                    className: "group ease-in-out duration-300 w-[24px] cursor-pointer relative",
                    href: "https://x.com/0xNADSA",
                    target: "_blank",
                    "data-bp": "1001-010",
                    children: [(0,
                    i.jsx)("img", {
                        src: "/images/footer/x.svg",
                        alt: "x",
                        className: "w-[24px] cursor-pointer"
                    }), (0,
                    i.jsx)(Bt, {
                        text: "X · @0xNADSA",
                        offset: 40
                    })]
                }), (0,
                i.jsxs)(G.default, {
                    className: "group ease-in-out duration-300 w-[24px] cursor-pointer relative",
                    href: "https://mirror.xyz/0xBd6E844F7DaCAF6339C26D5114F83986914803ef",
                    target: "_blank",
                    "data-bp": "1001-011",
                    children: [(0,
                    i.jsx)("img", {
                        src: "/images/footer/mirror.svg",
                        alt: "mirror",
                        className: "w-[24px] cursor-pointer"
                    }), (0,
                    i.jsx)(Bt, {
                        text: "Mirror"
                    })]
                }), (0,
                i.jsxs)(G.default, {
                    className: "group  ease-in-out duration-300 w-[24px] cursor-pointer relative",
                    href: "/terminal",
                    target: "_blank",
                    "data-bp": "1001-012",
                    children: [(0,
                    i.jsx)("img", {
                        src: "/images/footer/terminal.svg",
                        alt: "ternimal",
                        className: "w-[24px] cursor-pointer"
                    }), (0,
                    i.jsx)(Bt, {
                        text: "NADSA Terminal"
                    })]
                }), (0,
                i.jsxs)("div", {
                    className: "fixed right-[10px] bottom-[6px] z-50 flex items-center gap-[8px]",
                    children: [!e && (0,
                    i.jsx)(Ue, {}), (0,
                    i.jsx)(Ee, {})]
                })]
            })
        }
        ));
        const Bt = e=>{
            let {text: t, offset: n=0} = e;
            return (0,
            i.jsx)("div", {
                style: {
                    marginLeft: n
                },
                className: "absolute group-hover:opacity-100 opacity-0 transition-all duration-300 left-1/2 -translate-x-1/2 bottom-[110%] z-50",
                children: (0,
                i.jsxs)("div", {
                    className: "relative flex flex-col items-center",
                    children: [(0,
                    i.jsx)("div", {
                        className: "px-4 py-2 rounded-lg bg-[#1A1843CC] text-white font-bold-[300] text-[10px] shadow-lg font-Unbounded whitespace-nowrap",
                        children: t
                    }), (0,
                    i.jsx)("div", {
                        style: {
                            marginLeft: 2 * -n
                        },
                        className: "w-0 h-0 border-x-8 border-x-transparent border-t-[10px] mt-[-1px] border-t-[#423F6A]"
                    })]
                })
            })
        }
        ;
        var Et = n(64278);
        var Mt = n(44839)
          , Ut = n(57818)
          , Ot = n(41352);
        const Vt = (0,
        Ut.default)((()=>n.e(1478).then(n.bind(n, 41478))), {
            loadableGenerated: {
                webpack: ()=>[41478]
            },
            ssr: !1,
            loading: ()=>null
        })
          , Ht = [/^\/faucet$/, /^\/$/, /^\/codes$/];
        const Pt = "relative h-full flex flex-1 flex-col items-center hover:drop-shadow-[0px_0px_6px_#FFFFFF80] justify-center cursor-pointer gap-[3px] text-[#A6A6DB] hover:text-[#fff] transition-all duration-300"
          , Rt = " text-[11px] font-bold-[200] "
          , zt = "text-[#fff] drop-shadow-[0px_0px_6px_#FFFFFF80]"
          , Xt = "flex items-center justify-center h-[65px] gap-[10px] border-b border-[#38365E]"
          , Wt = "text-[#fff]  bg-[#00000026]"
          , Jt = "drop-shadow-[0px_0px_6px_#FFFFFF80]"
          , $t = ()=>(0,
        i.jsx)("svg", {
            className: "fill-current",
            style: {
                transform: "translateY(-6px)"
            },
            width: "28",
            height: "28",
            viewBox: "0 0 28 28",
            fill: "none",
            xmlns: "http://www.w3.org/2000/svg",
            children: (0,
            i.jsx)("g", {
                children: (0,
                i.jsx)("path", {
                    d: "M21.6358 13.016C20.715 11.932 15.9533 7.17211 15.3112 6.51171C14.56 5.73916 13.2999 5.92607 12.7304 6.51171C12.0883 7.15965 7.30241 11.932 6.36946 13.016C5.43652 14.1001 6.47851 15.072 7.20548 15.072H8.18689V20.6792C8.18689 21.4019 8.75635 21.9875 9.45908 21.9875H11.0584C11.5188 21.9875 11.8823 21.6137 11.9065 21.1527V18.7728C11.9065 17.0408 12.7668 16.019 14.039 16.019C15.3112 16.019 16.1714 17.0408 16.1714 18.7728V21.215H16.1836C16.232 21.6511 16.5955 22 17.0438 22H18.631C19.3338 22 19.9032 21.4144 19.9032 20.6917V15.0845H20.7998C21.5268 15.072 22.5567 14.0876 21.6358 13.016Z"
                })
            })
        })
          , Yt = ()=>(0,
        i.jsx)("svg", {
            className: "fill-current",
            width: "14",
            height: "16",
            viewBox: "0 0 14 16",
            fill: "none",
            xmlns: "http://www.w3.org/2000/svg",
            children: (0,
            i.jsx)("path", {
                d: "M0 0L1.55555 14.6C1.63334 15.4 2.2944 16 3.11116 16H10.8889C11.6667 16 12.3278 15.4 12.4445 14.6L14 0H0ZM7 13.6C5.71666 13.6 4.6667 12.52 4.6667 11.2C4.6667 9.60003 7 6.88002 7 6.88002C7 6.88002 9.33339 9.60005 9.33339 11.2C9.33341 12.52 8.28336 13.6 7 13.6ZM11.9389 4.80001H2.10001L1.75003 1.59998H12.289L11.9389 4.80001Z"
            })
        })
          , qt = ()=>(0,
        i.jsx)("svg", {
            className: "fill-current",
            width: "16",
            height: "13",
            viewBox: "0 0 16 13",
            fill: "none",
            xmlns: "http://www.w3.org/2000/svg",
            children: (0,
            i.jsx)("path", {
                d: "M15.1992 0C15.4113 5.48674e-05 15.6147 0.0843947 15.7646 0.234375C15.9146 0.384392 15.999 0.587689 15.999 0.799805V4.39941C15.4687 4.39941 14.96 4.61039 14.585 4.98535C14.21 5.36033 13.9991 5.86912 13.999 6.39941C13.999 6.92985 14.2099 7.43938 14.585 7.81445C14.96 8.18935 15.4688 8.39941 15.999 8.39941V12C15.999 12.2121 15.9146 12.4154 15.7646 12.5654C15.6147 12.7154 15.4113 12.7997 15.1992 12.7998H0.799805C0.5877 12.7998 0.38436 12.7154 0.234375 12.5654C0.0844017 12.4154 5.16566e-05 12.2121 0 12V8.39941C0.530282 8.39941 1.03904 8.18937 1.41406 7.81445C1.78911 7.43938 2 6.92985 2 6.39941C1.9999 5.86912 1.78902 5.36033 1.41406 4.98535C1.03903 4.61037 0.530328 4.39941 0 4.39941V0.799805C3.94526e-09 0.587674 0.084412 0.384395 0.234375 0.234375C0.384361 0.0843797 0.587697 3.79427e-05 0.799805 0H15.1992ZM6 8C5.3373 8 4.7998 8.53745 4.7998 9.2002C4.80002 9.86276 5.33743 10.3994 6 10.3994H9.99902C10.6616 10.3994 11.199 9.86276 11.1992 9.2002C11.1992 8.53745 10.6617 8 9.99902 8H6ZM6 3.2002C5.33743 3.2002 4.80002 3.73685 4.7998 4.39941C4.7998 5.06216 5.3373 5.59961 6 5.59961H9.99902C10.6617 5.59961 11.1992 5.06216 11.1992 4.39941C11.199 3.73685 10.6616 3.2002 9.99902 3.2002H6Z"
            })
        })
          , Gt = ()=>(0,
        i.jsx)("svg", {
            className: "fill-current",
            width: "13",
            height: "16",
            viewBox: "0 0 13 16",
            fill: "none",
            xmlns: "http://www.w3.org/2000/svg",
            children: (0,
            i.jsx)("path", {
                d: "M7 13L9 11.5L10 11C10 11.2294 10.2047 10.8853 10 11V6.5L7.26772 8.1721C7.16535 8.2868 7.06299 8.2868 6.96063 8.2868L7 13ZM12.1811 3.3549C12.6929 3.69899 13 4.27246 13 4.84594V11.1542C13 11.8424 12.6929 12.4158 12.1811 12.6452L7.26772 15.742C6.75591 16.0861 6.14173 16.0861 5.73228 15.742L0.818898 12.6452C0.307087 12.3011 0 11.7277 0 11.1542V4.84594C0 4.15777 0.307087 3.58429 0.818898 3.3549L5.73228 0.258126C6.24409 -0.0859605 6.85827 -0.0859605 7.26772 0.258126L12.1811 3.3549Z"
            })
        })
          , Kt = ()=>(0,
        i.jsx)("svg", {
            className: "fill-current",
            width: "16",
            height: "13",
            viewBox: "0 0 16 13",
            fill: "none",
            xmlns: "http://www.w3.org/2000/svg",
            children: (0,
            i.jsx)("path", {
                d: "M8 0C10.0736 0 11.9888 0.43176 13.416 1.16838C14.128 1.53515 14.7552 1.99708 15.2136 2.55651C15.6768 3.12136 16 3.82781 16 4.64258V8.35665C16 9.17142 15.6768 9.87786 15.2136 10.4427C14.7552 11.0021 14.1288 11.4641 13.416 11.8316C11.9888 12.5675 10.0736 13 8 13C5.9264 13 4.0112 12.5675 2.584 11.8316C1.872 11.4641 1.2448 11.0021 0.7864 10.4427C0.3232 9.87786 0 9.17142 0 8.35665V4.64258C0 3.82781 0.3232 3.12136 0.7864 2.55651C1.2448 1.99708 1.8712 1.53515 2.584 1.16761C4.0112 0.43176 5.9264 0 8 0ZM14.4 7.50241C14.0913 7.73494 13.762 7.94078 13.416 8.11755C11.9888 8.8534 10.0736 9.28593 8 9.28593C5.9264 9.28593 4.0112 8.8534 2.584 8.11755C2.232 7.93572 1.9 7.73067 1.6 7.50241V8.35665C1.6 8.56711 1.68 8.83793 1.9456 9.16213C2.2152 9.49098 2.6432 9.82989 3.2336 10.1348C4.4128 10.7422 6.0976 11.1422 8 11.1422C9.9032 11.1422 11.5872 10.7422 12.7664 10.1348C13.3568 9.82989 13.7848 9.49098 14.0544 9.16213C14.3208 8.83793 14.4 8.56711 14.4 8.35742V7.50241Z"
            })
        })
          , Qt = ()=>(0,
        i.jsx)("svg", {
            className: "fill-current",
            width: "17",
            height: "13",
            viewBox: "0 0 17 13",
            fill: "none",
            xmlns: "http://www.w3.org/2000/svg",
            children: (0,
            i.jsx)("path", {
                d: "M11.9985 0.306641C15.0333 0.306996 15.9892 3.6749 16.5297 8.37305C16.7792 10.8679 16.5715 12.9469 15.0746 12.9053C12.9129 12.9051 12.8294 10.952 11.2914 10.0371C10.7094 9.74607 9.42029 9.62069 8.33928 9.5791C7.25825 9.62068 5.96928 9.74607 5.38713 10.0371C3.84869 10.9519 3.76509 12.9053 1.60295 12.9053C0.0646627 12.9052 -0.185127 10.8267 0.105881 8.33203C0.604837 3.675 1.56178 0.306641 4.59709 0.306641C6.13531 0.306836 6.88373 1.88692 8.29729 2.01172C9.66941 1.88698 10.46 0.306641 11.9985 0.306641ZM4.63811 3.63281C3.43243 3.63297 2.476 4.63173 2.476 5.7959C2.47626 6.95986 3.4326 7.95785 4.63811 7.95801C5.80218 7.95801 6.79995 7.00151 6.80022 5.7959C6.80022 4.59006 5.84392 3.63281 4.63811 3.63281ZM11.7905 3.7168V5.37988H10.0854V6.21094H11.7905V7.91602H12.6215V6.21094H14.3266V5.37988H12.6215V3.7168H11.7905ZM4.63811 4.54883C4.97071 4.54886 5.30328 4.71595 5.51115 4.92383C5.7605 5.17327 5.88518 5.46432 5.88518 5.79688C5.88508 6.12942 5.71899 6.46208 5.51115 6.66992C5.26172 6.91932 4.97067 7.04392 4.63811 7.04395C4.30557 7.04395 3.97297 6.87774 3.76506 6.66992C3.51566 6.42051 3.39111 6.1294 3.39104 5.79688C3.39104 5.46427 3.51564 5.13174 3.76506 4.92383C4.01454 4.67434 4.30547 4.54883 4.63811 4.54883Z"
            })
        })
          , en = ()=>(0,
        i.jsx)("div", {
            className: "absolute bottom-0 left-[50%] -translate-x-[50%]",
            children: (0,
            i.jsxs)("svg", {
                width: "83",
                height: "1",
                viewBox: "0 0 83 1",
                fill: "none",
                xmlns: "http://www.w3.org/2000/svg",
                children: [(0,
                i.jsx)("line", {
                    y1: "0.499939",
                    x2: "83",
                    y2: "0.499939",
                    stroke: "url(#paint0_linear_242_40)"
                }), (0,
                i.jsx)("defs", {
                    children: (0,
                    i.jsxs)("linearGradient", {
                        id: "paint0_linear_242_40",
                        x1: "0",
                        y1: "1.49994",
                        x2: "83",
                        y2: "1.49994",
                        gradientUnits: "userSpaceOnUse",
                        children: [(0,
                        i.jsx)("stop", {
                            "stop-color": "white",
                            "stop-opacity": "0"
                        }), (0,
                        i.jsx)("stop", {
                            offset: "0.5",
                            "stop-color": "white"
                        }), (0,
                        i.jsx)("stop", {
                            offset: "1",
                            "stop-color": "white",
                            "stop-opacity": "0"
                        })]
                    })
                })]
            })
        });
        var tn = e=>{
            const {className: t, style: n} = e
              , s = (()=>{
                const e = (0,
                wt.useRouter)()
                  , t = (0,
                wt.usePathname)()
                  , n = (0,
                wt.useSearchParams)()
                  , {push: s, back: i} = e
                  , {run: a, cancel: r} = (0,
                X.Z)((()=>{
                    Et.done()
                }
                ), {
                    wait: 5e3
                });
                return e.push = (e,t)=>{
                    r(),
                    Et.start(),
                    a(),
                    s(e, t)
                }
                ,
                e.back = ()=>{
                    r(),
                    Et.start(),
                    a(),
                    i()
                }
                ,
                (0,
                P.d4)((()=>{
                    Et.done()
                }
                ), [t, n]),
                e
            }
            )()
              , a = (0,
            wt.usePathname)()
              , r = (0,
            Le.Z)()
              , [o,l] = (0,
            Ot.eJ)(!1)
              , [c,d] = (0,
            Ot.eJ)(!1);
            return (0,
            i.jsxs)("header", {
                className: (0,
                Mt.Z)("flex w-full lg:h-[60px] md:h-[40px] stroke-black font-CherryBomb top-0 z-50 bear-header", Ht.some((e=>e.test(a))) ? "fixed" : "sticky", t),
                style: n,
                children: [(0,
                i.jsx)("div", {
                    className: "min-w-[120px]",
                    children: ["/"].includes(a) ? (0,
                    i.jsx)("div", {}) : (0,
                    i.jsx)("div", {
                        "data-click-sound": "/audios/back-click-sound.mp3",
                        className: "cursor-pointer w-[120px]",
                        onClick: ()=>{
                            a.includes("/dex/") ? s.push("/dapps") : a.includes("2048") || a.includes("777") ? s.replace("/arcade") : s.replace("/")
                        }
                        ,
                        children: r ? (0,
                        i.jsx)("img", {
                            className: "mt-[10px] ml-[10px]",
                            src: "/images/header/back_button_mobile.svg",
                            alt: "back_button"
                        }) : (0,
                        i.jsx)("img", {
                            src: "/images/header/back_button.svg",
                            alt: "back_button"
                        })
                    })
                }), (0,
                i.jsxs)("div", {
                    className: (0,
                    Mt.Z)("flex-1", r ? "w-[100vw] absolute left-0 top-0 right-0" : "flex items-center justify-center relative"),
                    children: [!r && (0,
                    i.jsxs)("div", {
                        className: "absolute px-[30px] flex items-center justify-between left-1/2 -translate-x-1/2 top-0 bg-[url('/images/header/menu_bg.svg')] bg-contain bg-no-repeat bg-center w-[806px] h-[52px] font-Unbounded",
                        children: [(0,
                        i.jsxs)("div", {
                            className: (0,
                            Mt.Z)(Pt, "/" === a ? zt : ""),
                            onClick: ()=>{
                                "/" !== a && s.push("/")
                            }
                            ,
                            children: [(0,
                            i.jsx)("div", {
                                className: "h-[16px] mt-[4px]",
                                children: (0,
                                i.jsx)($t, {})
                            }), (0,
                            i.jsx)("div", {
                                className: (0,
                                Mt.Z)(Rt, ""),
                                children: "Hall"
                            }), "/" === a && (0,
                            i.jsx)(en, {})]
                        }), (0,
                        i.jsxs)("div", {
                            className: (0,
                            Mt.Z)(Pt, "/faucet" === a ? zt : ""),
                            onClick: ()=>{
                                "/faucet" !== a && s.push("/faucet")
                            }
                            ,
                            children: [(0,
                            i.jsx)(Yt, {}), (0,
                            i.jsx)("div", {
                                className: (0,
                                Mt.Z)(Rt, ""),
                                children: "Faucet"
                            }), "/faucet" === a && (0,
                            i.jsx)(en, {})]
                        }), (0,
                        i.jsxs)("div", {
                            className: (0,
                            Mt.Z)(Pt, "/codes" === a ? zt : ""),
                            onClick: ()=>{
                                "/codes" !== a && s.push("/codes")
                            }
                            ,
                            children: [(0,
                            i.jsx)(qt, {}), (0,
                            i.jsx)("div", {
                                className: (0,
                                Mt.Z)(Rt, ""),
                                children: "Codes"
                            }), "/codes" === a && (0,
                            i.jsx)(en, {})]
                        }), (0,
                        i.jsx)("div", {
                            className: "w-[200px]"
                        }), (0,
                        i.jsxs)("div", {
                            className: (0,
                            Mt.Z)(Pt, a.includes("/dapps") || a.includes("/dex/") ? zt : ""),
                            onClick: ()=>{
                                "/dapps" !== a && s.push("/dapps")
                            }
                            ,
                            children: [(0,
                            i.jsx)(Gt, {}), (0,
                            i.jsx)("div", {
                                className: (0,
                                Mt.Z)(Rt, ""),
                                children: "DApps"
                            }), (a.includes("/dapps") || a.includes("/dex/")) && (0,
                            i.jsx)(en, {})]
                        }), (0,
                        i.jsxs)("div", {
                            className: (0,
                            Mt.Z)(Pt, "/marketplace" === a ? zt : ""),
                            onClick: ()=>{
                                "/marketplace" !== a && s.push("/marketplace")
                            }
                            ,
                            children: [(0,
                            i.jsx)(Kt, {}), (0,
                            i.jsx)("div", {
                                className: (0,
                                Mt.Z)(Rt, ""),
                                children: "Tokens"
                            }), "/marketplace" === a && (0,
                            i.jsx)(en, {})]
                        }), (0,
                        i.jsxs)("div", {
                            className: (0,
                            Mt.Z)(Pt, a.includes("/arcade") ? zt : ""),
                            onClick: ()=>{
                                "/arcade" !== a && s.push("/arcade")
                            }
                            ,
                            children: [(0,
                            i.jsx)(Qt, {}), (0,
                            i.jsx)("div", {
                                className: (0,
                                Mt.Z)(Rt, ""),
                                children: "Arcade"
                            }), a.includes("/arcade") && (0,
                            i.jsx)(en, {})]
                        })]
                    }), r && (0,
                    i.jsxs)(Pe.E.div, {
                        onClick: ()=>{
                            l(!1)
                        }
                        ,
                        className: "w-[100vw] bg-[#1A1843] text-[#A6A6DB] font-Unbounded text-[16px] overflow-hidden border-b border-[#38365E]",
                        initial: {
                            height: 0
                        },
                        animate: {
                            height: o ? "auto" : 0,
                            paddingBottom: o ? "20px" : 0
                        },
                        transition: {
                            duration: .3,
                            ease: "easeInOut"
                        },
                        children: [(0,
                        i.jsxs)("div", {
                            className: (0,
                            Mt.Z)(Xt, "/" === a ? Wt : ""),
                            onClick: ()=>{
                                s.push("/")
                            }
                            ,
                            children: [(0,
                            i.jsxs)("div", {
                                className: (0,
                                Mt.Z)("flex items-center justify-center gap-[10px]", "/" === a ? Jt : ""),
                                children: [(0,
                                i.jsx)("div", {
                                    className: "translate-x-[-10px] translate-y-[6px]",
                                    children: (0,
                                    i.jsx)($t, {})
                                }), (0,
                                i.jsx)("div", {
                                    className: (0,
                                    Mt.Z)(Rt, "translate-x-[-18px]"),
                                    children: "Hall"
                                })]
                            }), "/" === a && (0,
                            i.jsx)(en, {})]
                        }), (0,
                        i.jsx)("div", {
                            className: (0,
                            Mt.Z)(Xt, "/faucet" === a ? Wt : ""),
                            onClick: ()=>{
                                s.push("/faucet")
                            }
                            ,
                            children: (0,
                            i.jsxs)("div", {
                                className: (0,
                                Mt.Z)("flex items-center justify-center gap-[10px]", "/faucet" === a ? Jt : ""),
                                children: [(0,
                                i.jsx)(Yt, {}), (0,
                                i.jsx)("div", {
                                    className: (0,
                                    Mt.Z)(Rt, ""),
                                    children: "Faucet"
                                })]
                            })
                        }), (0,
                        i.jsx)("div", {
                            className: (0,
                            Mt.Z)(Xt, "/codes" === a ? Wt : ""),
                            onClick: ()=>{
                                s.push("/codes")
                            }
                            ,
                            children: (0,
                            i.jsxs)("div", {
                                className: (0,
                                Mt.Z)("flex items-center justify-center gap-[10px]", "/codes" === a ? Jt : ""),
                                children: [(0,
                                i.jsx)(qt, {}), (0,
                                i.jsx)("div", {
                                    className: (0,
                                    Mt.Z)(Rt, ""),
                                    children: "Codes"
                                })]
                            })
                        }), (0,
                        i.jsx)("div", {
                            className: (0,
                            Mt.Z)(Xt, a.includes("/dapps") || a.includes("/dex/") ? Wt : ""),
                            onClick: ()=>{
                                s.push("/dapps")
                            }
                            ,
                            children: (0,
                            i.jsxs)("div", {
                                className: (0,
                                Mt.Z)("flex items-center justify-center gap-[10px]", a.includes("/dapps") || a.includes("/dex/") ? Jt : ""),
                                children: [(0,
                                i.jsx)(Gt, {}), (0,
                                i.jsx)("div", {
                                    className: (0,
                                    Mt.Z)(Rt, ""),
                                    children: "DApps"
                                })]
                            })
                        }), (0,
                        i.jsx)("div", {
                            className: (0,
                            Mt.Z)(Xt, "/marketplace" === a ? Wt : ""),
                            onClick: ()=>{
                                s.push("/marketplace")
                            }
                            ,
                            children: (0,
                            i.jsxs)("div", {
                                className: (0,
                                Mt.Z)("flex items-center justify-center gap-[10px]", "/marketplace" === a ? Jt : ""),
                                children: [(0,
                                i.jsx)(Kt, {}), (0,
                                i.jsx)("div", {
                                    className: (0,
                                    Mt.Z)(Rt, ""),
                                    children: "Tokens"
                                })]
                            })
                        }), (0,
                        i.jsx)("div", {
                            className: (0,
                            Mt.Z)(Xt, a.includes("/arcade") ? Wt : ""),
                            onClick: ()=>{
                                d(!0)
                            }
                            ,
                            children: (0,
                            i.jsxs)("div", {
                                className: (0,
                                Mt.Z)("flex items-center justify-center gap-[10px]", a.includes("/arcade") ? Jt : ""),
                                children: [(0,
                                i.jsx)(Qt, {}), (0,
                                i.jsx)("div", {
                                    className: (0,
                                    Mt.Z)(Rt, ""),
                                    children: "Arcade"
                                })]
                            })
                        })]
                    }), (0,
                    i.jsx)("div", {
                        onClick: ()=>{
                            r ? l(!o) : s.push("/")
                        }
                        ,
                        className: (0,
                        Mt.Z)("bg-contain relative bg-no-repeat bg-center hover:cursor-pointer", r ? "w-[160px] h-[40px] bg-[url('/images/mobile/logo.svg')] mx-auto mt-[-2px]" : "w-[240px] h-[60px] bg-[url('/images/header/logo_bg.svg')]")
                    })]
                }), (0,
                i.jsx)("div", {
                    className: "min-w-[120px] flex justify-end",
                    children: r ? (0,
                    i.jsx)("div", {
                        className: "w-[40px] h-[40px] flex items-center",
                        children: (0,
                        i.jsx)(Ue, {})
                    }) : (0,
                    i.jsx)(Ft, {})
                }), r && c && (0,
                i.jsx)(Vt, {
                    onClose: ()=>{
                        d(!1)
                    }
                })]
            })
        }
        ;
        var nn = R((e=>{
            const {children: t, style: n} = e;
            J();
            const {handleTrack: s} = (0,
            z.Z)()
              , {initializePrice: a} = function() {
                const e = (0,
                Y.O)((e=>e.set))
                  , [t,n] = (0,
                $.eJ)(!1)
                  , s = (0,
                $.I4)((async()=>{
                    if (!t) {
                        n(!0);
                        try {
                            const t = await o.U2("/token/price");
                            e({
                                price: t.data || {}
                            }),
                            setTimeout((()=>{
                                s()
                            }
                            ), 3e5),
                            n(!1)
                        } catch (i) {
                            n(!1)
                        }
                    }
                }
                ), [t]);
                return {
                    initializePrice: s
                }
            }()
              , {handleReportNoCode: l} = (0,
            z.Z)();
            (0,
            r.useEffect)((()=>{
                l(),
                a()
            }
            ), []);
            const {address: c} = (0,
            yt.m)()
              , {getAccessToken: d} = (0,
            q.Z)();
            return (0,
            r.useEffect)((()=>{
                d()
            }
            ), [c]),
            (0,
            i.jsxs)("div", {
                id: "layout",
                className: (0,
                Mt.Z)("min-h-screen relative flex flex-col items-stretch justify-start transition-background duration-150 bg-[#0E0F29]"),
                onClick: s,
                style: {
                    ...n
                },
                children: [(0,
                i.jsx)(tn, {}), (0,
                i.jsx)("div", {
                    className: "relative grow",
                    children: t
                }), (0,
                i.jsx)(It, {})]
            })
        }
        ));
        function sn(e) {
            let {children: t} = e;
            return t
        }
        var an = n(3918)
          , rn = n(94094);
        var on = e=>{
            const {children: t, className: n, ...s} = e;
            return (0,
            i.jsx)("button", {
                "data-bp": "1007-001",
                type: "button",
                className: (0,
                Mt.Z)("h-[36px] px-[35px] rounded-[6px] bg-[#A6A6D2] disabled:bg-[#6D7EA5] disabled:!cursor-not-allowed border border-black filter shadow-[0px_6px_0px_#000] disabled:active:shadow-[0px_6px_0px_#000] active:shadow-[0px_2px_0px_#000] active:drop-shadow-[0px_0px_10px_#7961FF] disabled:active:!drop-shadow-none active:translate-y-1 disabled:active:translate-y-0 active:bg-[#A191FF] disabled:active:!bg-[#6D7EA5] text-black text-center font-Unbounded text-[12px] font-[500] leading-[100%] transition-all duration-100", n),
                ...s,
                children: t
            })
        }
        ;
        var ln = e=>{
            const {className: t} = e
              , {account: n} = (0,
            Se.Z)()
              , s = (0,
            Le.Z)()
              , [a,r] = (0,
            Ot.eJ)(!1)
              , {invitationCode: o, handleInvitationCodeChange: l, handleInvitationCodeBackspace: c, handleInvitationCodeKeyboard: d, submitInvitationCode: u, submitInvitationCodeLoading: x, invalidInvitationCode: p} = (0,
            U.DO)()
              , m = !n || x || !(0,
            an.trim)(o) || (0,
            an.trim)(o).length < rn.B || p;
            return (0,
            i.jsxs)("div", {
                className: (0,
                Mt.Z)("w-[252px] shrink-0 grid grid-cols-3 gap-[12px] [transform-style:preserve-3d] [transform:perspective(1000px)_rotate3d(1,_0,_0,_30deg)_scale(1.1,_1.3)_skewX(4deg)] [transform-origin:bottom] [perspective-origin:60%_35%]", t),
                children: [(0,
                i.jsx)("input", {
                    type: "text",
                    className: (0,
                    Mt.Z)("cursor-default col-span-2 h-[45.655px] shrink-0 border bg-black border-[#55648A] rounded-[6px] shadow-[inset_3px_3px_0px_0px_#2C3635] text-[#A5FFFD] text-center font-Unbounded text-[16px] font-[500] leading-[100%] placeholder:text-[#A5FFFD]", p && "!text-[#FF5372]"),
                    placeholder: a ? "" : "Invite Code",
                    value: o,
                    onChange: e=>l(e.target.value),
                    onFocus: ()=>r(!0),
                    onBlur: ()=>r(!1)
                }), s ? (0,
                i.jsx)(dn, {
                    disabled: m,
                    onClick: u,
                    invalidInvitationCode: p
                }) : (0,
                i.jsx)(cn, {
                    onChange: e=>{
                        l(e)
                    }
                }), [...new Array(9)].map(((e,t)=>(0,
                i.jsx)(on, {
                    className: "",
                    onClick: ()=>d((t + 1).toString()),
                    children: t + 1
                }, t))), (0,
                i.jsx)(on, {
                    className: "flex justify-center items-center !p-0",
                    onClick: ()=>c(),
                    children: (0,
                    i.jsx)("img", {
                        src: "/images/invitation/icon-backspace.svg",
                        alt: "",
                        className: "object-center object-contain w-[20px] h-[14px] shrink-0"
                    })
                }), (0,
                i.jsx)(on, {
                    className: "",
                    onClick: ()=>d("0"),
                    children: "0"
                }), s ? (0,
                i.jsx)(cn, {
                    onChange: e=>{
                        l(e)
                    }
                }) : (0,
                i.jsx)(dn, {
                    disabled: m,
                    onClick: u,
                    invalidInvitationCode: p
                })]
            })
        }
        ;
        const cn = e=>{
            const {className: t, onChange: n} = e
              , s = (0,
            Le.Z)();
            return (0,
            i.jsx)(on, {
                className: (0,
                Mt.Z)("flex justify-center items-center !px-[0px]", s ? "" : "!h-[40px]", t),
                onClick: async()=>{
                    try {
                        const e = await navigator.clipboard.readText();
                        n(e)
                    } catch (e) {}
                }
                ,
                children: (0,
                i.jsx)("img", {
                    src: "/images/invitation/icon-copy.svg",
                    alt: "",
                    className: "object-center object-contain w-[16px] h-[16px] shrink-0"
                })
            })
        }
          , dn = e=>{
            const {className: t, onClick: n, disabled: s, invalidInvitationCode: a} = e
              , r = (0,
            Le.Z)();
            return (0,
            i.jsx)(on, {
                className: (0,
                Mt.Z)("!px-[0] active:!drop-shadow-[0px_0px_10px_rgba(120,254,255,0.60)]", a ? "!bg-[#FF5372] !drop-shadow-[0px_0px_10px_#FF5372]" : s ? "!bg-[#6D7EA5]" : "!bg-[#A5FFFD]", r ? "!h-[46px]" : "", t),
                disabled: s,
                onClick: n,
                children: "Enter"
            })
        }
        ;
        var un = e=>{
            const {account: t} = (0,
            Se.Z)();
            return (0,
            i.jsx)("div", {
                className: "w-[675px] shrink-0 rounded-2xl border border-black [background:linear-gradient(180deg,_#899BCF_-3.4%,_#545F7D_100%)] shadow-[inset_0px_2px_0px_0px_rgba(255,255,255,0.25)] text-white font-Unbounded text-[15px] font-light leading-[150%] p-2",
                children: (0,
                i.jsx)("div", {
                    className: "w-full p-[8px_36px] rounded-xl border border-black bg-black shadow-[inset_0px_2px_0px_0px_rgba(255,255,255,0.25)]",
                    children: t ? (0,
                    i.jsxs)(i.Fragment, {
                        children: [(0,
                        i.jsx)("div", {
                            className: "text-[20px] font-[500]",
                            children: "Don’t have an access code? You can:"
                        }), (0,
                        i.jsxs)("ul", {
                            className: "mt-[8px] list-disc pl-[20px]",
                            children: [(0,
                            i.jsxs)("li", {
                                className: "",
                                children: ["Pickup clues in the ", (0,
                                i.jsx)(G.default, {
                                    className: "text-[#78FEFF] underline underline-offset-2",
                                    prefetch: !0,
                                    href: "/terminal?from=invitation",
                                    children: "Terminal"
                                })]
                            }), (0,
                            i.jsxs)("li", {
                                className: "",
                                children: ["Pickup clues from NADSA official ", (0,
                                i.jsx)(G.default, {
                                    className: "text-[#78FEFF] underline underline-offset-2",
                                    href: "https://x.com/0xNADSA",
                                    target: "_blank",
                                    children: "X"
                                })]
                            }), (0,
                            i.jsx)("li", {
                                className: "",
                                children: "Get a code from all Monad ecosystem projects"
                            })]
                        })]
                    }) : (0,
                    i.jsx)(xn, {})
                })
            })
        }
        ;
        const xn = e=>{
            const {className: t, isBr: n=!0} = e;
            return (0,
            i.jsxs)("div", {
                className: (0,
                Mt.Z)("text-center pt-[20px] pb-[24px]", t),
                children: ["Access NADSA One by verifying your ", (0,
                i.jsx)(G.default, {
                    className: "text-[#78FEFF] underline underline-offset-2",
                    href: "https://monad-testnet.socialscan.io/address/".concat("0x006300ab0e8969c88dcf2e7c070e6c29b6081c1f"),
                    target: "_blank",
                    children: "Admission Ticket"
                }), " or", n && (0,
                i.jsx)("br", {}), " entering your Access Code."]
            })
        }
        ;
        var pn = n(69548);
        var mn = e=>{
            const {className: t} = e
              , {account: n} = (0,
            Se.Z)()
              , {isConnecting: s} = (0,
            yt.m)()
              , {openConnectModal: a} = (0,
            y.We)()
              , {disconnect: r} = (0,
            We.q)();
            return (0,
            i.jsxs)(on, {
                className: (0,
                Mt.Z)("!h-[46px] w-full flex justify-center items-center gap-[20px]", n && "!bg-[#6D7EA5]", t),
                disabled: !1,
                onClick: ()=>{
                    n ? r() : null == a || a()
                }
                ,
                children: [(0,
                i.jsx)("span", {
                    children: n ? (0,
                    pn.Fo)(n, 5, 6) : "Connect Wallet"
                }), n && (0,
                i.jsx)("button", {
                    type: "button",
                    className: "w-[16px] h-[16px] shrink-0",
                    children: (0,
                    i.jsx)("img", {
                        src: "/images/invitation/icon-logout.svg",
                        alt: "logout",
                        className: "w-full h-full object-contain object-center"
                    })
                })]
            })
        }
        ;
        var hn = ()=>{
            const {finalValid: e} = (0,
            U.DO)();
            return e ? null : (0,
            i.jsxs)("div", {
                className: "fixed top-0 left-0 w-screen h-screen z-[101] bg-black bg-[url('/images/mobile/bg-boat-window.png')] bg-no-repeat bg-top bg-[auto_300px] overflow-y-auto pb-[20dvh]",
                children: [(0,
                i.jsx)("img", {
                    src: "/images/logo-green.svg",
                    alt: "",
                    className: "w-[134px] h-[55px] object-contain mx-auto mt-[45px]"
                }), (0,
                i.jsx)(Pe.E.img, {
                    src: "/images/invitation/bg-ticket-straight.png",
                    alt: "",
                    className: "w-[231px] h-[143px] object-contain mx-auto mt-[38px]",
                    initial: {
                        scale: .1
                    },
                    animate: {
                        scale: 1
                    },
                    transition: {
                        delay: .3
                    }
                }), (0,
                i.jsx)(xn, {
                    className: "!pt-[36px] !text-white !text-[13px] !pb-[unset]",
                    isBr: !1
                }), (0,
                i.jsx)("div", {
                    className: "px-[20px] w-full mt-[36px]",
                    children: (0,
                    i.jsx)(mn, {
                        className: "!bg-[#78FEFF]"
                    })
                }), (0,
                i.jsx)("div", {
                    className: "px-[15px] mt-[25px]",
                    children: (0,
                    i.jsx)("div", {
                        className: "w-full border-t border-[rgba(166,166,219,0.5)] flex justify-center",
                        children: (0,
                        i.jsx)("div", {
                            className: "w-[40px] bg-black text-[#A6A6DB] text-center font-Unbounded text-[13px] font-light leading-[150%] -translate-y-[10px]",
                            children: "or"
                        })
                    })
                }), (0,
                i.jsx)("div", {
                    className: "mt-[20px] px-[20px]",
                    children: (0,
                    i.jsx)(ln, {
                        className: "![transform:unset] !w-full"
                    })
                })]
            })
        }
        ;
        var fn = e=>{
            const {delay: t=0, className: n} = e;
            return (0,
            i.jsx)(Pe.E.div, {
                className: (0,
                Mt.Z)("w-[2.564px] h-[2.564px] shrink-0 rounded-full bg-[#78FEFF] shadow-[0px_0px_4px_0px_#A5FFFD]", n),
                initial: {
                    scale: 0,
                    opacity: 0
                },
                animate: {
                    scale: 1,
                    opacity: 1
                },
                transition: {
                    type: "spring",
                    stiffness: 200,
                    damping: 10,
                    delay: t,
                    repeat: 1 / 0,
                    repeatDelay: 3
                }
            })
        }
        ;
        var gn = e=>{
            const {account: t} = (0,
            Se.Z)()
              , {loading: n, validUser: s} = (0,
            U.DO)();
            return (0,
            i.jsx)("div", {
                className: "w-[765px] h-[390px] overflow-hidden shrink-0",
                children: (0,
                i.jsxs)("div", {
                    className: "w-full h-[431px] pt-[37px] flex justify-center items-start gap-[87px] bg-[url('/images/invitation/bg-code.png')] bg-no-repeat bg-contain bg-center",
                    children: [(0,
                    i.jsxs)("div", {
                        className: "w-[258px] shrink-0 flex flex-col justify-center items-center gap-[20px] [transform-style:preserve-3d] [transform:perspective(1000px)_rotate3d(1,_0,_0,_25deg)_scale(1.1,_1.2)_skewX(-4.2deg)_translateX(-10px)] [transform-origin:bottom] [perspective-origin:60%_35%]",
                        children: [(0,
                        i.jsx)("div", {
                            className: "w-full",
                            children: (0,
                            i.jsx)(mn, {})
                        }), (0,
                        i.jsxs)("div", {
                            className: "relative w-full h-[186px] flex justify-center items-center bg-black [background-image:linear-gradient(to_right,_rgba(120,254,255,0.1)_1px,_transparent_1px),_linear-gradient(to_bottom,_rgba(120,254,255,0.1)_1px,_transparent_1px)] bg-[length:25px_25px] bg-[position:-1px_-1px] rounded-[6px]",
                            children: [(0,
                            i.jsx)(fn, {
                                delay: 0,
                                className: "absolute top-[24px] left-[24px]"
                            }), (0,
                            i.jsx)(fn, {
                                delay: 1,
                                className: "absolute top-[24px] left-[174px]"
                            }), (0,
                            i.jsx)(fn, {
                                delay: 2,
                                className: "absolute top-[98px] left-[248px]"
                            }), (0,
                            i.jsx)(fn, {
                                delay: 1,
                                className: "absolute top-[173px] left-[24px]"
                            }), (0,
                            i.jsx)(fn, {
                                delay: 0,
                                className: "absolute top-[173px] left-[224px]"
                            }), (0,
                            i.jsx)(Pe.E.div, {
                                className: "w-[218px] h-[135px] shrink-0 bg-[url('/images/invitation/bg-ticket.png')] bg-no-repeat bg-contain bg-center",
                                style: {
                                    x: 8
                                },
                                initial: {
                                    scale: 0,
                                    opacity: 0
                                },
                                animate: {
                                    scale: 1,
                                    opacity: 1
                                },
                                transition: {
                                    type: "spring",
                                    stiffness: 200,
                                    damping: 20,
                                    delay: .5
                                }
                            }), !s && !n && !!t && (0,
                            i.jsx)("div", {
                                className: "w-full h-full absolute overflow-hidden",
                                children: (0,
                                i.jsxs)(Pe.E.div, {
                                    className: "w-full h-full flex justify-center items-center flex-col gap-[20px] left-0 top-0 bg-[rgba(0,0,0,0.5)] rounded-[6px]",
                                    style: {
                                        x: 8
                                    },
                                    initial: {
                                        scale: 0,
                                        opacity: 0
                                    },
                                    animate: {
                                        scale: 1,
                                        opacity: 1
                                    },
                                    transition: {
                                        duration: .15,
                                        delay: .5
                                    },
                                    children: [(0,
                                    i.jsxs)("div", {
                                        className: "w-[198px] h-[61px] flex justify-center items-center shrink-0 bg-[rgba(0,0,0,0.6)] border border-[#78FEFF] text-[#A5FFFD] text-center font-Unbounded text-[14px] font-[400] leading-[120%]",
                                        children: ["No Pass found in", (0,
                                        i.jsx)("br", {}), " this wallet"]
                                    }), (0,
                                    i.jsxs)("div", {
                                        className: "text-[#A5FFFD] font-Unbounded text-[12px] font-[400] leading-[120%]",
                                        children: ["Try to ", (0,
                                        i.jsx)(G.default, {
                                            prefetch: !0,
                                            href: "/terminal?from=invitation",
                                            className: "underline underline-offset-2 cursor-pointer",
                                            children: "get a ticket"
                                        }), " or", (0,
                                        i.jsx)("br", {}), " input an invite code"]
                                    })]
                                })
                            })]
                        })]
                    }), (0,
                    i.jsx)(ln, {})]
                })
            })
        }
        ;
        const vn = {
            width: "33.89vw",
            minWidth: "633px",
            height: "33.19vw",
            minHeight: "620px",
            bottom: "6.7vw"
        }
          , bn = {
            width: "61vh",
            minWidth: "633px",
            height: "61vh",
            minHeight: "620px",
            bottom: "11.2vh"
        };
        var jn = e=>{
            const {} = e
              , t = (0,
            Ve.mT)()
              , [n,s] = (0,
            Ve.eJ)({
                ...vn,
                left: "calc(50% - 31.80vw)"
            })
              , [a,r] = (0,
            Ve.eJ)({
                ...vn,
                right: "calc(50% - 31.80vw)"
            })
              , [o,l] = (0,
            Ve.eJ)({
                backgroundSize: "cover"
            })
              , {scopeLeftDoor: c, scopeRightDoor: d, scopeCodePad: u, scopeInvitation: x, finalValid: p} = (0,
            U.DO)();
            return (0,
            Ve.d4)((()=>{
                const e = ()=>{
                    if (t.current) {
                        const {offsetWidth: e, offsetHeight: n} = t.current
                          , i = e / n
                          , a = 1772 / 1070;
                        if (e <= 1772 && n <= 1070)
                            return s({
                                ...vn,
                                minWidth: "640px",
                                minHeight: "630px",
                                bottom: "124px",
                                left: "calc(50% - 598px)"
                            }),
                            r({
                                ...vn,
                                minWidth: "640px",
                                minHeight: "630px",
                                bottom: "124px",
                                right: "calc(50% - 598px)"
                            }),
                            void l({
                                backgroundSize: "cover"
                            });
                        if (i < a)
                            s({
                                ...bn,
                                left: "calc(50% - 57vh)"
                            }),
                            r({
                                ...bn,
                                right: "calc(50% - 57vh)"
                            }),
                            l({
                                backgroundSize: "cover"
                            });
                        else {
                            s({
                                ...vn,
                                left: "calc(50% - 31.80vw)"
                            }),
                            r({
                                ...vn,
                                right: "calc(50% - 31.80vw)"
                            });
                            const e = 60.8 * i - .9;
                            l({
                                backgroundSize: "".concat(e, "% ").concat(e, "%")
                            })
                        }
                    }
                }
                ;
                e();
                const n = ()=>{
                    e()
                }
                ;
                window.addEventListener("resize", n);
                const i = new ResizeObserver((()=>{
                    e()
                }
                ));
                return t.current && i.observe(t.current),
                ()=>{
                    window.removeEventListener("resize", n),
                    i.disconnect()
                }
            }
            ), []),
            p ? null : (0,
            i.jsxs)(Pe.E.div, {
                ref: x,
                className: "fixed top-0 left-0 w-screen h-screen z-[101]",
                children: [(0,
                i.jsxs)("div", {
                    className: "relative z-[2] w-full h-full flex justify-center items-stretch",
                    children: [(0,
                    i.jsx)("div", {
                        className: "flex-1 h-full bg-black bg-[url('/images/invitation/bg-wall.png')] bg-no-repeat bg-cover bg-bottom",
                        style: o
                    }), (0,
                    i.jsx)(Pe.E.div, {
                        ref: t,
                        className: "w-[94.45vw] min-w-[1772px] h-full shrink-0 bg-[url('/images/invitation/bg-gate-full.png')] bg-no-repeat bg-cover bg-bottom",
                        children: (0,
                        i.jsxs)(Pe.E.div, {
                            ref: u,
                            className: "flex flex-col justify-end items-center gap-[14px] w-full h-full",
                            children: [(0,
                            i.jsx)(un, {}), (0,
                            i.jsx)(gn, {})]
                        })
                    }), (0,
                    i.jsx)("div", {
                        className: "flex-1 h-full bg-black bg-[url('/images/invitation/bg-wall.png')] bg-no-repeat bg-cover bg-bottom",
                        style: o
                    })]
                }), (0,
                i.jsx)(Pe.E.img, {
                    ref: c,
                    src: "/images/invitation/bg-door-left.png",
                    alt: "",
                    className: "invitation-bg-door invitation-bg-door-left absolute z-[1] object-contain object-right",
                    style: n
                }), (0,
                i.jsx)(Pe.E.img, {
                    ref: d,
                    src: "/images/invitation/bg-door-right.png",
                    alt: "",
                    className: "invitation-bg-door invitation-bg-door-right absolute z-[1] object-contain object-left",
                    style: a
                })]
            })
        }
        ;
        var wn = e=>(0,
        Le.Z)() ? (0,
        i.jsx)(hn, {
            ...e
        }) : (0,
        i.jsx)(jn, {
            ...e
        })
          , Cn = n(57878);
        function yn(e) {
            let {children: t} = e;
            const n = (0,
            wt.usePathname)()
              , {validUser: s} = (0,
            U.DO)();
            (0,
            Le.Z)();
            return ["/terminal", "/terminal/login"].includes(n) ? (0,
            i.jsx)(sn, {
                children: t
            }) : (0,
            i.jsxs)(nn, {
                children: [(0,
                i.jsx)(wn, {}), s ? t : null, (0,
                i.jsx)(Cn.Z, {})]
            })
        }
        var Nn = n(62737)
          , kn = n.n(Nn)
          , _n = n(8558)
          , Zn = n.n(_n)
          , Fn = n(85811)
          , An = n(31877)
          , Dn = (n(3436),
        n(95956));
        n(44193),
        n(53054);
        function Tn(e) {
            let {children: t} = e;
            return (0,
            i.jsxs)("html", {
                lang: "en",
                className: "md:overflow-hidden",
                children: [(0,
                i.jsxs)("head", {
                    children: [(0,
                    i.jsx)("title", {
                        children: "NADSA"
                    }), (0,
                    i.jsx)("meta", {
                        name: "description",
                        content: "Effortlessly explore & dive into all dApps in the Monad ecosystem from one streamlined hub."
                    }), (0,
                    i.jsx)("link", {
                        rel: "icon",
                        href: "/images/favicon.ico"
                    }), (0,
                    i.jsx)("link", {
                        rel: "icon",
                        href: "/favicon.ico"
                    })]
                }), (0,
                i.jsxs)("body", {
                    className: "md:overflow-hidden",
                    children: [(0,
                    i.jsx)(M, {
                        children: (0,
                        i.jsx)(Ct.y, {
                            baseColor: "#7990F4",
                            highlightColor: "#8B87FF",
                            children: (0,
                            i.jsx)(v, {
                                children: (0,
                                i.jsx)(U.ZP, {
                                    children: (0,
                                    i.jsx)(r.Suspense, {
                                        children: (0,
                                        i.jsx)(yn, {
                                            children: t
                                        })
                                    })
                                })
                            })
                        })
                    }), (0,
                    i.jsx)(Dn.Ix, {
                        position: "top-right",
                        autoClose: 5e3,
                        hideProgressBar: !0,
                        theme: "light",
                        toastStyle: {
                            backgroundColor: "transparent",
                            boxShadow: "none"
                        },
                        newestOnTop: !0,
                        rtl: !1,
                        pauseOnFocusLoss: !0,
                        closeButton: !1,
                        limit: 3
                    }), (0,
                    i.jsx)(Fn.Cd, {
                        height: "4px",
                        color: "#8B87FF",
                        options: {
                            showSpinner: !1
                        },
                        shallowRouting: !0
                    })]
                }), (0,
                i.jsx)(An.default, {
                    async: !0,
                    src: "https://www.googletagmanager.com/gtag/js?id=G-SZ82B6ZN43"
                }), (0,
                i.jsx)(An.default, {
                    id: "ga-config",
                    children: "\n          window.dataLayer = window.dataLayer || [];\n          function gtag(){dataLayer.push(arguments);}\n          gtag('js', new Date());\n          gtag('config', 'G-SZ82B6ZN43');"
                })]
            })
        }
        kn().extend(Zn())
    },
    96509: function(e, t, n) {
        "use strict";
        n.d(t, {
            Z: function() {
                return a
            }
        });
        var s = n(57437)
          , i = n(2265)
          , a = (0,
        i.memo)((0,
        i.forwardRef)((function(e, t) {
            let {src: n="/audios/dapps/clipping.mp3", config: a} = e;
            const r = (0,
            i.useRef)()
              , o = {
                muted: e=>{
                    r.current && (r.current.muted = e)
                }
                ,
                play: ()=>{
                    if (r.current) {
                        r.current.currentTime = 0;
                        try {
                            r.current.play()
                        } catch (e) {
                            throw new Error(e)
                        }
                    }
                }
                ,
                pause: ()=>{
                    r.current && r.current.pause()
                }
            };
            return (0,
            i.useImperativeHandle)(t, (()=>o)),
            (0,
            s.jsx)(s.Fragment, {
                children: (0,
                s.jsx)("audio", {
                    src: n,
                    ref: r,
                    className: "absolute -left-[9999px] -top-[9999px]",
                    ...a
                })
            })
        }
        )))
    },
    5927: function(e, t, n) {
        "use strict";
        var s = n(57437)
          , i = n(88469)
          , a = n(78055)
          , r = n(44839);
        t.Z = e=>{
            const {onClick: t, type: n="primary", disabled: o, loading: l, isOnlyLoading: c, className: d, style: u, htmlType: x="button", children: p, dataBp: m, bgColor: h="#FFDC50"} = e;
            return (0,
            s.jsx)(i.E.button, {
                className: (0,
                r.Z)("h-[32px] px-[10.5px] leading-[30px] border border-[#373A53] rounded-[10px] text-center text-[16px] font-[500] text-black disabled:!opacity-30 disabled:!cursor-not-allowed", l ? "opacity-30" : "", d),
                style: {
                    background: "primary" === n ? h : "#ffffff",
                    ...u
                },
                disabled: o,
                type: x,
                whileHover: o ? {} : {
                    background: h
                },
                onClick: t,
                "data-bp": m,
                children: c ? l ? (0,
                s.jsx)(a.Z, {
                    size: 14
                }) : p : l ? (0,
                s.jsxs)("div", {
                    className: "flex justify-center items-center gap-[5px]",
                    children: [(0,
                    s.jsx)(a.Z, {
                        size: 14
                    }), p]
                }) : p
            })
        }
    },
    59080: function(e, t, n) {
        "use strict";
        n.d(t, {
            m9: function() {
                return d
            },
            xo: function() {
                return u
            },
            ZP: function() {
                return x
            }
        });
        var s = n(57437)
          , i = n(41079)
          , a = n(34446)
          , r = n(88469)
          , o = n(2265)
          , l = n(43477)
          , c = n(92115);
        var d, u, x = (0,
        o.forwardRef)(((e,t)=>{
            const {children: n, content: a, placement: r=8, offset: d=5, trigger: u="click", contentStyle: x, contentClassName: m, triggerContainerStyle: h, triggerContainerClassName: f, onClickBefore: g, closeDelayDuration: v=300} = e
              , b = (0,
            o.useRef)()
              , j = (0,
            c.Z)()
              , [w,C] = (0,
            o.useState)(!1)
              , [y,N] = (0,
            o.useState)(!1)
              , [k,_] = (0,
            o.useState)(0)
              , [Z,F] = (0,
            o.useState)(0)
              , {run: A, cancel: D} = (0,
            l.Z)((()=>{
                C(!1),
                N(!1)
            }
            ), {
                wait: v
            })
              , T = {
                onClose: ()=>{
                    C(!1),
                    N(!1)
                }
                ,
                onOpen: ()=>{
                    C(!0)
                }
            };
            return (0,
            o.useImperativeHandle)(t, (()=>T)),
            "undefined" == typeof document ? n : (0,
            s.jsxs)(s.Fragment, {
                children: [(0,
                s.jsx)("div", {
                    ref: b,
                    style: h,
                    className: f,
                    onClick: async e=>{
                        if (j || "hover" !== u) {
                            if (j && e.stopPropagation(),
                            g) {
                                if (!await g(e))
                                    return
                            }
                            C(!0)
                        }
                    }
                    ,
                    onMouseEnter: ()=>{
                        j || "click" === u || (D(),
                        C(!0))
                    }
                    ,
                    onMouseLeave: ()=>{
                        j || "click" === u || A()
                    }
                    ,
                    children: n
                }), w && (0,
                i.jz)((0,
                s.jsx)(p, {
                    x: k,
                    y: Z,
                    onLoaded: e=>{
                        const t = b.current
                          , {width: n, height: s, x: i, y: a} = t.getBoundingClientRect()
                          , {width: o, height: l} = e.getBoundingClientRect()
                          , c = i + n / 2
                          , u = a + s / 2
                          , x = i + o / 2
                          , p = a + l / 2;
                        let m = 0
                          , h = 0;
                        9 === r && (m = i + n - o,
                        h = a + s + d),
                        2 === r && (m = i - (x - c),
                        h = a + s + d),
                        8 === r && (m = i,
                        h = a + s + d),
                        11 === r && (m = i - o - d,
                        h = a - (l - s)),
                        3 === r && (m = i - o - d,
                        h = a - (p - u)),
                        10 === r && (m = i - o - d,
                        h = a),
                        4 === r && (m = i,
                        h = a - d - l),
                        0 === r && (m = i - (x - c),
                        h = a - d - l),
                        5 === r && (m = i + n - o,
                        h = a - d - l),
                        6 === r && (m = i + n + d,
                        h = a),
                        1 === r && (m = i + n + d,
                        h = a - (p - u)),
                        7 === r && (m = i + n + d,
                        h = a - (l - s)),
                        12 === r && (m = i + 2 * d + (n - o) / 2,
                        h = a + d + (s - l) / 2),
                        m < 0 && (m = 0),
                        m > window.innerWidth - o && (m = window.innerWidth - o),
                        h < 0 && (h = 0),
                        h > window.innerHeight - l && (h = window.innerHeight - l),
                        _(m),
                        F(h),
                        N(!0)
                    }
                    ,
                    visible: y,
                    onClose: ()=>{
                        N(!1),
                        C(!1)
                    }
                    ,
                    style: x,
                    className: m,
                    setVisible: C,
                    closeDelay: A,
                    closeCancel: D,
                    trigger: j ? "click" : u,
                    children: a
                }), document.body)]
            })
        }
        ));
        !function(e) {
            e[e.Top = 0] = "Top",
            e[e.Right = 1] = "Right",
            e[e.Bottom = 2] = "Bottom",
            e[e.Left = 3] = "Left",
            e[e.TopLeft = 4] = "TopLeft",
            e[e.TopRight = 5] = "TopRight",
            e[e.RightTop = 6] = "RightTop",
            e[e.RightBottom = 7] = "RightBottom",
            e[e.BottomLeft = 8] = "BottomLeft",
            e[e.BottomRight = 9] = "BottomRight",
            e[e.LeftTop = 10] = "LeftTop",
            e[e.LeftBottom = 11] = "LeftBottom",
            e[e.Center = 12] = "Center"
        }(d || (d = {})),
        function(e) {
            e.Click = "click",
            e.Hover = "hover"
        }(u || (u = {}));
        const p = e=>{
            const {onLoaded: t, x: n, y: i, visible: l, onClose: c, children: d, style: u, className: x, setVisible: p, closeDelay: m, closeCancel: h, trigger: f} = e
              , g = (0,
            o.useRef)(null);
            return (0,
            o.useEffect)((()=>{
                if (!g.current)
                    return;
                t(g.current);
                const e = e=>{
                    g.current.contains(e.target) || c()
                }
                ;
                return document.addEventListener("click", e),
                ()=>{
                    document.removeEventListener("click", e)
                }
            }
            ), []),
            (0,
            s.jsx)(a.M, {
                mode: "wait",
                children: (0,
                s.jsx)(r.E.div, {
                    className: "fixed z-[12] left-0 top-0 ".concat(x),
                    ref: g,
                    style: {
                        left: n,
                        top: i,
                        visibility: l ? "visible" : "hidden",
                        ...u
                    },
                    animate: {
                        opacity: 1,
                        x: 0,
                        transition: {
                            type: "spring",
                            stiffness: 200,
                            damping: 15,
                            duration: 1
                        }
                    },
                    exit: {
                        opacity: 0
                    },
                    initial: {
                        opacity: 0
                    },
                    onMouseEnter: ()=>{
                        "click" !== f && (p(!0),
                        h())
                    }
                    ,
                    onMouseLeave: ()=>{
                        "click" !== f && m()
                    }
                    ,
                    onClick: e=>{
                        e.stopPropagation()
                    }
                    ,
                    children: d
                })
            })
        }
    },
    13807: function(e, t, n) {
        "use strict";
        n.d(t, {
            ZP: function() {
                return b
            },
            DO: function() {
                return j
            }
        });
        var s = n(57437)
          , i = n(13883)
          , a = n(23920)
          , r = n(7781)
          , o = n(73421)
          , l = n(94094)
          , c = n(44496)
          , d = n(40270)
          , u = n(65975)
          , x = n(37399)
          , p = n(64773)
          , m = n(81062)
          , h = n(3918)
          , f = n(60749)
          , g = n(92115);
        const v = (0,
        i.kr)({});
        var b = function(e) {
            let {children: t} = e;
            const n = function() {
                const {account: e, accountWithAk: t} = (0,
                r.Z)()
                  , {hasNFT: n, checking: s} = (0,
                d.e)({
                    nftAddress: "0x006300ab0e8969c88dcf2e7c070e6c29b6081c1f",
                    autoChecking: !1
                })
                  , i = (0,
                x.Z)()
                  , v = (0,
                p.L)((e=>e.user))
                  , b = (0,
                p.L)((e=>e.set))
                  , j = (0,
                p.L)((e=>e.loading))
                  , {getUserInfo: w} = (0,
                m.Z)()
                  , {getVisited: C, setVisible: y} = (0,
                f.c)()
                  , N = (0,
                g.Z)()
                  , [k,_] = (0,
                c.H)()
                  , [Z,F] = (0,
                c.H)()
                  , [A,D] = (0,
                c.H)()
                  , [T,S] = (0,
                c.H)()
                  , L = (0,
                a.mT)()
                  , I = (0,
                a.mT)()
                  , B = (0,
                a.mT)()
                  , [E,M] = (0,
                a.eJ)(!1)
                  , [U,O] = (0,
                a.eJ)(!1)
                  , [V,H] = (0,
                a.eJ)(!1)
                  , [P,R] = (0,
                a.eJ)("")
                  , z = ()=>k.current && Z.current && A.current && T.current && !N ? (_(k.current, {
                    x: "-100%"
                }, {
                    duration: 1,
                    ease: "easeInOut"
                }),
                F(Z.current, {
                    x: "100%"
                }, {
                    duration: 1,
                    ease: "easeInOut"
                }),
                D(A.current, {
                    y: "100%"
                }, {
                    duration: 1,
                    ease: "easeInOut"
                }),
                new Promise((t=>{
                    L.current = setTimeout((()=>{
                        S(T.current, {
                            scale: 10,
                            opacity: 0
                        }, {
                            duration: 3,
                            ease: "easeInOut"
                        }),
                        I.current = setTimeout((()=>{
                            clearTimeout(I.current),
                            t(!0)
                        }
                        ), 2e3),
                        clearTimeout(L.current),
                        B.current = setTimeout((()=>{
                            clearTimeout(B.current),
                            C(e) || y(!0)
                        }
                        ), 2e3)
                    }
                    ), 1100)
                }
                ))) : Promise.resolve(!0)
                  , X = (0,
                a.Ye)((()=>!(!e || !n && !E && !(null == v ? void 0 : v.invite_active))), [n, e, E, v])
                  , {runAsync: W, loading: J} = (0,
                o.Z)((async()=>{
                    if (!P)
                        return;
                    const e = await (0,
                    u.v_)("/invite/active", {
                        code: P
                    });
                    return 200 !== e.code ? (i.fail({
                        title: e.message || "Invalid Invitation Code"
                    }),
                    void O(!0)) : (w(),
                    M(!0),
                    await z(),
                    H(!0),
                    e)
                }
                ), {
                    manual: !0
                })
                  , {runAsync: $, loading: Y, data: q} = (0,
                o.Z)((async()=>{
                    if (!e)
                        return []
                }
                ), {
                    manual: !0
                })
                  , G = (0,
                a.Ye)((()=>j || s), [j, s])
                  , {data: K} = (0,
                o.Z)((async()=>{
                    if (!X || !t)
                        return;
                    const e = await (0,
                    u.v_)("/invite/timestamp");
                    return 200 === e.code ? (b({
                        inviteTimestamp: e.data
                    }),
                    e.data) : void 0
                }
                ), {
                    refreshDeps: [X, t]
                });
                return (0,
                a.d4)((()=>{
                    X ? z().then((()=>{
                        C(e) || y(!0),
                        H(!0)
                    }
                    )) : (H(!1),
                    y(!1))
                }
                ), [X]),
                (0,
                a.d4)((()=>()=>{
                    clearTimeout(L.current),
                    clearTimeout(I.current),
                    clearTimeout(B.current)
                }
                ), []),
                {
                    loading: G,
                    hasNFT: n,
                    nftLoading: s,
                    validUser: X,
                    invitationCode: P,
                    handleInvitationCodeChange: e=>{
                        const t = null != e ? e : "";
                        R((0,
                        h.trim)(t).slice(0, l.B)),
                        O(!1)
                    }
                    ,
                    handleInvitationCodeBackspace: ()=>{
                        R((e=>0 === e.length ? "" : e.slice(0, e.length - 1))),
                        O(!1)
                    }
                    ,
                    handleInvitationCodeKeyboard: e=>{
                        R((t=>{
                            const n = (null != t ? t : "") + (null != e ? e : "");
                            return n.length > l.B ? e || "" : n.slice(0, l.B)
                        }
                        )),
                        O(!1)
                    }
                    ,
                    submitInvitationCode: W,
                    submitInvitationCodeLoading: J,
                    getInvitationList: $,
                    getInvitationListLoading: Y,
                    invitationList: q,
                    scopeLeftDoor: k,
                    scopeRightDoor: Z,
                    scopeCodePad: A,
                    scopeInvitation: T,
                    finalValid: V,
                    invalidInvitationCode: U
                }
            }();
            return (0,
            s.jsx)(v.Provider, {
                value: {
                    ...n
                },
                children: t
            })
        };
        const j = ()=>(0,
        i.qp)(v)
    },
    94094: function(e, t, n) {
        "use strict";
        n.d(t, {
            B: function() {
                return s
            }
        });
        const s = 8
    },
    57878: function(e, t, n) {
        "use strict";
        n.d(t, {
            m: function() {
                return u
            },
            Z: function() {
                return d
            }
        });
        var s = n(57437)
          , i = n(2825)
          , a = n(59080)
          , r = n(60749)
          , o = n(44839)
          , l = n(92115)
          , c = n(2265);
        var d = ()=>{
            const {setVisitedIndex: e, getVisitedIndex: t, visible: n, setIsMint: a, setVisible: o, max: l, isMint: c} = (0,
            r.c)();
            return n ? (0,
            s.jsx)(i.Z, {
                isOpen: n,
                isMint: c,
                setVisitedIndex: e,
                getVisitedIndex: t,
                closeModal: ()=>{
                    o(!1),
                    a(!1)
                }
            }) : null
        }
        ;
        const u = (0,
        c.forwardRef)(((e,t)=>{
            const {className: n} = e
              , i = (0,
            c.useRef)()
              , {setVisible: d, setIsMint: u} = (0,
            r.c)()
              , x = (0,
            l.Z)()
              , p = {
                openPopover: ()=>{
                    i.current && i.current.onOpen()
                }
                ,
                closePopover: ()=>{
                    i.current && i.current.onClose()
                }
            };
            return (0,
            c.useImperativeHandle)(t, (()=>p)),
            (0,
            s.jsx)(a.ZP, {
                ref: i,
                trigger: a.xo.Hover,
                placement: a.m9.Top,
                content: x ? (0,
                s.jsx)("div", {
                    className: "w-[40px] h-[44px] pt-[12px] flex justify-center bg-[url('/images/invitation/bg-popup-mobile.png')] bg-no-repeat bg-center bg-contain text-[#FFF] font-Unbounded text-[12px] font-[400] leading-[150%]",
                    children: (0,
                    s.jsx)("div", {
                        className: "rotate-[6.538deg]",
                        children: "?!"
                    })
                }) : (0,
                s.jsxs)("div", {
                    className: "w-[220px] h-[77px] flex justify-center pt-[15px] bg-[url('/images/invitation/bg-popup.png')] bg-no-repeat bg-center bg-contain text-white font-Unbounded text-xs font-normal leading-[150%]",
                    children: ["Who dropped this? ", (0,
                    s.jsx)("br", {}), "It looks very rare"]
                }),
                triggerContainerClassName: (0,
                o.Z)("w-[60px] h-[78px] shrink-0 rounded-[4px] border-2 border-[rgba(0,0,0,0)] hover:border-[#78FEFF] hover:shadow-[-10px_10px_0px_0px_rgba(0,_0,_0,_0.50)] hover:[transform:perspective(1000px)_rotateX(30deg)_rotateY(-30deg)_scale(1,_1.1)_skewX(40deg)_translateY(-4px)] transition-border duration-300 overflow-hidden shadow-[-6px_6px_0px_0px_rgba(0,_0,_0,_0.50)] [transform-style:preserve-3d] [transform:perspective(1000px)_rotateX(30deg)_rotateY(-30deg)_scale(1,_1.1)_skewX(40deg)] [transform-origin:bottom] [perspective-origin:60%_35%]", n),
                closeDelayDuration: 0,
                children: (0,
                s.jsx)("img", {
                    src: "/images/invitation/guide-entry.png",
                    alt: "",
                    className: "w-full h-full object-center object-contain cursor-pointer",
                    onClick: ()=>{
                        u(!0),
                        d(!0)
                    }
                })
            })
        }
        ))
    },
    25177: function(e, t, n) {
        "use strict";
        n.d(t, {
            w: function() {
                return a
            }
        });
        var s = n(10903)
          , i = n(89291);
        const a = (0,
        s.U)((0,
        i.tJ)((e=>({
            muted: !0,
            conveyorBeltRef: null,
            set: t=>e((()=>({
                ...t
            })))
        })), {
            name: "_sound_0519",
            storage: (0,
            i.FL)((()=>sessionStorage))
        }))
    },
    89755: function(e, t, n) {
        "use strict";
        n.d(t, {
            O: function() {
                return s
            }
        });
        const s = (0,
        n(10903).U)((e=>({
            price: {},
            set: t=>e((()=>({
                ...t
            })))
        })))
    },
    53054: function() {}
}, function(e) {
    e.O(0, [949, 9141, 8374, 9109, 5975, 8746, 6889, 1574, 8173, 2703, 5683, 9707, 3216, 2599, 881, 3421, 231, 4496, 5811, 8042, 1336, 2712, 5372, 422, 9187, 757, 3335, 1744], (function() {
        return t = 73450,
        e(e.s = t);
        var t
    }
    ));
    var t = e.O();
    _N_E = t
}
]);
